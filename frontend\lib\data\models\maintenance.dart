import 'package:json_annotation/json_annotation.dart';

part 'maintenance.g.dart';

@JsonSerializable()
class MaintenanceIssue {
  final String id;
  final String propertyId;
  final String title;
  final String description;
  final String status; // OPEN, IN_PROGRESS, RESOLVED, CLOSED
  final String priority; // LOW, MEDIUM, HIGH, CRITICAL
  final String department; // ELECTRICITY, WATER, SECURITY, GENERAL
  final String? assignedTo;
  final String? assignedToName;
  final String reportedBy;
  final String reportedByName;
  final String reportedDate;
  final String? resolvedDate;
  final String? estimatedCost;
  final String? actualCost;
  final String? recurrence; // NONE, DAILY, WEEKLY, MONTHLY, YEARLY
  final List<String>? attachments;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const MaintenanceIssue({
    required this.id,
    required this.propertyId,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    required this.department,
    this.assignedTo,
    this.assignedToName,
    required this.reportedBy,
    required this.reportedByName,
    required this.reportedDate,
    this.resolvedDate,
    this.estimatedCost,
    this.actualCost,
    this.recurrence,
    this.attachments,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory MaintenanceIssue.fromJson(Map<String, dynamic> json) => _$MaintenanceIssueFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceIssueToJson(this);

  MaintenanceIssue copyWith({
    String? id,
    String? propertyId,
    String? title,
    String? description,
    String? status,
    String? priority,
    String? department,
    String? assignedTo,
    String? assignedToName,
    String? reportedBy,
    String? reportedByName,
    String? reportedDate,
    String? resolvedDate,
    String? estimatedCost,
    String? actualCost,
    String? recurrence,
    List<String>? attachments,
    String? createdAt,
    String? updatedAt,
    PropertyInfo? property,
  }) {
    return MaintenanceIssue(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      department: department ?? this.department,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedToName: assignedToName ?? this.assignedToName,
      reportedBy: reportedBy ?? this.reportedBy,
      reportedByName: reportedByName ?? this.reportedByName,
      reportedDate: reportedDate ?? this.reportedDate,
      resolvedDate: resolvedDate ?? this.resolvedDate,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      actualCost: actualCost ?? this.actualCost,
      recurrence: recurrence ?? this.recurrence,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      property: property ?? this.property,
    );
  }

  // Helper getters
  bool get isOpen => status == 'OPEN';
  bool get isInProgress => status == 'IN_PROGRESS';
  bool get isResolved => status == 'RESOLVED';
  bool get isClosed => status == 'CLOSED';

  bool get isLowPriority => priority == 'LOW';
  bool get isMediumPriority => priority == 'MEDIUM';
  bool get isHighPriority => priority == 'HIGH';
  bool get isCriticalPriority => priority == 'CRITICAL';

  bool get isRecurring => recurrence != null && recurrence != 'NONE';
  bool get hasAttachments => attachments != null && attachments!.isNotEmpty;
  bool get isAssigned => assignedTo != null;

  DateTime get reportedDateTime => DateTime.parse(reportedDate);
  DateTime? get resolvedDateTime => resolvedDate != null ? DateTime.parse(resolvedDate!) : null;
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  Duration? get resolutionTime => resolvedDateTime?.difference(reportedDateTime);
  int? get resolutionDays => resolutionTime?.inDays;

  String get propertyName => property?.name ?? 'Unknown Property';
}

@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String type;
  final String address;

  const PropertyInfo({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

@JsonSerializable()
class MaintenanceIssueDetail extends MaintenanceIssue {
  final List<MaintenanceComment> comments;
  final List<MaintenanceActivity> activities;
  final List<MaintenanceAttachment> attachmentDetails;

  const MaintenanceIssueDetail({
    required super.id,
    required super.propertyId,
    required super.title,
    required super.description,
    required super.status,
    required super.priority,
    required super.department,
    super.assignedTo,
    super.assignedToName,
    required super.reportedBy,
    required super.reportedByName,
    required super.reportedDate,
    super.resolvedDate,
    super.estimatedCost,
    super.actualCost,
    super.recurrence,
    super.attachments,
    required super.createdAt,
    required super.updatedAt,
    super.property,
    required this.comments,
    required this.activities,
    required this.attachmentDetails,
  });

  factory MaintenanceIssueDetail.fromJson(Map<String, dynamic> json) => _$MaintenanceIssueDetailFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$MaintenanceIssueDetailToJson(this);
}

@JsonSerializable()
class MaintenanceComment {
  final String id;
  final String issueId;
  final String userId;
  final String userName;
  final String comment;
  final String createdAt;

  const MaintenanceComment({
    required this.id,
    required this.issueId,
    required this.userId,
    required this.userName,
    required this.comment,
    required this.createdAt,
  });

  factory MaintenanceComment.fromJson(Map<String, dynamic> json) => _$MaintenanceCommentFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceCommentToJson(this);

  DateTime get createdDateTime => DateTime.parse(createdAt);
}

@JsonSerializable()
class MaintenanceActivity {
  final String id;
  final String issueId;
  final String userId;
  final String userName;
  final String action; // CREATED, ASSIGNED, STATUS_CHANGED, RESOLVED, etc.
  final String? oldValue;
  final String? newValue;
  final String createdAt;

  const MaintenanceActivity({
    required this.id,
    required this.issueId,
    required this.userId,
    required this.userName,
    required this.action,
    this.oldValue,
    this.newValue,
    required this.createdAt,
  });

  factory MaintenanceActivity.fromJson(Map<String, dynamic> json) => _$MaintenanceActivityFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceActivityToJson(this);

  DateTime get createdDateTime => DateTime.parse(createdAt);
}

@JsonSerializable()
class MaintenanceAttachment {
  final String id;
  final String issueId;
  final String fileName;
  final String fileType;
  final int fileSize;
  final String url;
  final String uploadedBy;
  final String uploadedByName;
  final String createdAt;

  const MaintenanceAttachment({
    required this.id,
    required this.issueId,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    required this.url,
    required this.uploadedBy,
    required this.uploadedByName,
    required this.createdAt,
  });

  factory MaintenanceAttachment.fromJson(Map<String, dynamic> json) => _$MaintenanceAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceAttachmentToJson(this);

  DateTime get createdDateTime => DateTime.parse(createdAt);
  String get fileSizeFormatted => _formatFileSize(fileSize);

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

@JsonSerializable()
class MaintenanceStatistics {
  final int totalIssues;
  final int openIssues;
  final int inProgressIssues;
  final int resolvedIssues;
  final int closedIssues;
  final int criticalIssues;
  final int highPriorityIssues;
  final int mediumPriorityIssues;
  final int lowPriorityIssues;
  final double averageResolutionTime; // in hours
  final Map<String, int> issuesByDepartment;
  final Map<String, int> issuesByProperty;
  final List<MaintenanceTrend> trends;

  const MaintenanceStatistics({
    required this.totalIssues,
    required this.openIssues,
    required this.inProgressIssues,
    required this.resolvedIssues,
    required this.closedIssues,
    required this.criticalIssues,
    required this.highPriorityIssues,
    required this.mediumPriorityIssues,
    required this.lowPriorityIssues,
    required this.averageResolutionTime,
    required this.issuesByDepartment,
    required this.issuesByProperty,
    required this.trends,
  });

  factory MaintenanceStatistics.fromJson(Map<String, dynamic> json) => _$MaintenanceStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceStatisticsToJson(this);

  // Helper getters
  double get resolutionRate => totalIssues > 0 ? (resolvedIssues + closedIssues) / totalIssues * 100 : 0;
  double get criticalRate => totalIssues > 0 ? criticalIssues / totalIssues * 100 : 0;
  int get pendingIssues => openIssues + inProgressIssues;
  double get pendingRate => totalIssues > 0 ? pendingIssues / totalIssues * 100 : 0;

  String get averageResolutionTimeFormatted {
    if (averageResolutionTime < 24) {
      return '${averageResolutionTime.toStringAsFixed(1)} hours';
    } else {
      final days = (averageResolutionTime / 24).toStringAsFixed(1);
      return '$days days';
    }
  }
}

@JsonSerializable()
class MaintenanceTrend {
  final String date;
  final int created;
  final int resolved;
  final int pending;

  const MaintenanceTrend({
    required this.date,
    required this.created,
    required this.resolved,
    required this.pending,
  });

  factory MaintenanceTrend.fromJson(Map<String, dynamic> json) => _$MaintenanceTrendFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceTrendToJson(this);

  DateTime get dateTime => DateTime.parse(date);
}
