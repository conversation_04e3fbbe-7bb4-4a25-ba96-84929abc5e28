import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';
import '../../data/models/office.dart';
import 'api_client.dart';

class OfficeService {
  static final OfficeService _instance = OfficeService._internal();
  factory OfficeService() => _instance;
  OfficeService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get list of offices with statistics
  Future<ApiResponse<List<OfficeWithStats>>> getOffices({
    String? type,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (type != null) queryParams['type'] = type;

      final response = await _apiClient.get<List<OfficeWithStats>>(
        ApiConstants.officesList,
        queryParameters: queryParams,
        fromJson: (json) => (json as List)
            .map((item) => OfficeWithStats.fromJson(item as Map<String, dynamic>))
            .toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<OfficeWithStats>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch offices: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Create new office
  Future<ApiResponse<Office>> createOffice({
    required String name,
    required String type,
    required String address,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? workingHours,
  }) async {
    try {
      final data = {
        'name': name,
        'type': type,
        'address': address,
        if (latitude != null) 'latitude': latitude,
        if (longitude != null) 'longitude': longitude,
        if (workingHours != null) 'workingHours': workingHours,
      };

      final response = await _apiClient.post<Office>(
        ApiConstants.officesList,
        data: data,
        fromJson: (json) => Office.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<Office>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to create office: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get employees for an office
  Future<ApiResponse<List<Employee>>> getOfficeEmployees(String officeId) async {
    try {
      final path = ApiConstants.officeEmployees.replaceAll('{officeId}', officeId);

      final response = await _apiClient.get<List<Employee>>(
        path,
        fromJson: (json) => (json as List)
            .map((item) => Employee.fromJson(item as Map<String, dynamic>))
            .toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Employee>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch employees: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Add employee to office
  Future<ApiResponse<Employee>> addEmployee({
    required String officeId,
    required String name,
    String? email,
    String? phone,
    required String employeeId,
    required String designation,
    String? department,
    required String joinDate,
  }) async {
    try {
      final data = {
        'name': name,
        'employeeId': employeeId,
        'designation': designation,
        'joinDate': joinDate,
        if (email != null) 'email': email,
        if (phone != null) 'phone': phone,
        if (department != null) 'department': department,
      };

      final path = ApiConstants.officeEmployees.replaceAll('{officeId}', officeId);

      final response = await _apiClient.post<Employee>(
        path,
        data: data,
        fromJson: (json) => Employee.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<Employee>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to add employee: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get attendance records for an office
  Future<ApiResponse<AttendanceResponse>> getAttendance({
    required String officeId,
    String? date,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (date != null) queryParams['date'] = date;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final path = ApiConstants.officeAttendance.replaceAll('{officeId}', officeId);

      final response = await _apiClient.get<AttendanceResponse>(
        path,
        queryParameters: queryParams,
        fromJson: (json) => AttendanceResponse.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<AttendanceResponse>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch attendance: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Submit attendance records
  Future<ApiResponse<AttendanceSubmissionResponse>> submitAttendance({
    required String officeId,
    required String date,
    required List<AttendanceRecordInput> records,
  }) async {
    try {
      final data = {
        'date': date,
        'records': records.map((r) => r.toJson()).toList(),
      };

      final path = ApiConstants.officeAttendance.replaceAll('{officeId}', officeId);

      final response = await _apiClient.post<AttendanceSubmissionResponse>(
        path,
        data: data,
        fromJson: (json) => AttendanceSubmissionResponse.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<AttendanceSubmissionResponse>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to submit attendance: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get office types for dropdown
  List<String> getOfficeTypes() {
    return ApiConstants.officeTypes;
  }

  // Get attendance statuses for dropdown
  List<String> getAttendanceStatuses() {
    return ApiConstants.attendanceStatuses;
  }

  // Helper method to get office type display name
  String getOfficeTypeDisplayName(String type) {
    switch (type) {
      case 'OFFICE':
        return 'Office';
      case 'CONSTRUCTION_SITE':
        return 'Construction Site';
      default:
        return type;
    }
  }

  // Helper method to get attendance status display info
  Map<String, dynamic> getAttendanceStatusInfo(String status) {
    switch (status) {
      case 'PRESENT':
        return {
          'name': 'Present',
          'color': 0xFF4CAF50, // Green
          'icon': 'check_circle',
        };
      case 'ABSENT':
        return {
          'name': 'Absent',
          'color': 0xFFF44336, // Red
          'icon': 'cancel',
        };
      case 'LATE':
        return {
          'name': 'Late',
          'color': 0xFFFF9800, // Orange
          'icon': 'schedule',
        };
      case 'HALF_DAY':
        return {
          'name': 'Half Day',
          'color': 0xFF2196F3, // Blue
          'icon': 'access_time',
        };
      case 'LEAVE':
        return {
          'name': 'Leave',
          'color': 0xFF9C27B0, // Purple
          'icon': 'event_busy',
        };
      default:
        return {
          'name': status,
          'color': 0xFF9E9E9E,
          'icon': 'help',
        };
    }
  }

  // Calculate attendance statistics
  Map<String, dynamic> calculateAttendanceStats(List<AttendanceRecord> records) {
    final total = records.length;
    final present = records.where((r) => r.isPresent).length;
    final absent = records.where((r) => r.isAbsent).length;
    final late = records.where((r) => r.isLate).length;
    final halfDay = records.where((r) => r.isHalfDay).length;
    final leave = records.where((r) => r.isLeave).length;
    
    final attendanceRate = total > 0 ? (present / total) * 100 : 0.0;
    
    return {
      'total': total,
      'present': present,
      'absent': absent,
      'late': late,
      'halfDay': halfDay,
      'leave': leave,
      'attendanceRate': attendanceRate,
      'presentPercentage': total > 0 ? (present / total) * 100 : 0,
      'absentPercentage': total > 0 ? (absent / total) * 100 : 0,
    };
  }

  // Format working hours for display
  String formatWorkingHours(Map<String, dynamic>? workingHours) {
    if (workingHours == null) return 'Not specified';
    
    final monday = workingHours['monday'];
    if (monday != null && monday['start'] != null && monday['end'] != null) {
      return '${monday['start']} - ${monday['end']}';
    }
    
    return 'Not specified';
  }

  // Check if office is currently open
  bool isOfficeOpen(Map<String, dynamic>? workingHours) {
    if (workingHours == null) return false;
    
    final now = DateTime.now();
    final dayName = _getDayName(now.weekday);
    final dayHours = workingHours[dayName];
    
    if (dayHours == null || dayHours['closed'] == true) return false;
    
    final startTime = dayHours['start'];
    final endTime = dayHours['end'];
    
    if (startTime == null || endTime == null) return false;
    
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    return currentTime.compareTo(startTime) >= 0 && currentTime.compareTo(endTime) <= 0;
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'monday';
      case 2: return 'tuesday';
      case 3: return 'wednesday';
      case 4: return 'thursday';
      case 5: return 'friday';
      case 6: return 'saturday';
      case 7: return 'sunday';
      default: return 'monday';
    }
  }
}

// Helper class for attendance input
class AttendanceRecordInput {
  final String? employeeId;
  final String? userId;
  final String status;
  final String? checkInTime;
  final String? checkOutTime;
  final double? hoursWorked;
  final double? overtime;
  final String? notes;

  const AttendanceRecordInput({
    this.employeeId,
    this.userId,
    required this.status,
    this.checkInTime,
    this.checkOutTime,
    this.hoursWorked,
    this.overtime,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      if (employeeId != null) 'employeeId': employeeId,
      if (userId != null) 'userId': userId,
      'status': status,
      if (checkInTime != null) 'checkInTime': checkInTime,
      if (checkOutTime != null) 'checkOutTime': checkOutTime,
      if (hoursWorked != null) 'hoursWorked': hoursWorked,
      if (overtime != null) 'overtime': overtime,
      if (notes != null) 'notes': notes,
    };
  }
}

// Response model for attendance submission
class AttendanceSubmissionResponse {
  final bool success;
  final int processed;
  final int failed;
  final List<String> errors;
  final String message;

  const AttendanceSubmissionResponse({
    required this.success,
    required this.processed,
    required this.failed,
    required this.errors,
    required this.message,
  });

  factory AttendanceSubmissionResponse.fromJson(Map<String, dynamic> json) {
    return AttendanceSubmissionResponse(
      success: json['success'] ?? false,
      processed: json['processed'] ?? 0,
      failed: json['failed'] ?? 0,
      errors: List<String>.from(json['errors'] ?? []),
      message: json['message'] ?? '',
    );
  }
}
