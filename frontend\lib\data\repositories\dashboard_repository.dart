import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard.dart';
import '../models/alert.dart';
import '../models/api_response.dart';
import '../../core/services/dashboard_service.dart';

class DashboardRepository {
  final DashboardService _dashboardService = DashboardService();
  static const String _cacheKeyPrefix = 'dashboard_cache_';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Get Dashboard Overview with caching
  Future<ApiResponse<DashboardOverview>> getDashboardOverview({
    List<String>? propertyIds,
    String timeRange = '24h',
    bool forceRefresh = false,
  }) async {
    final cacheKey = '${_cacheKeyPrefix}overview_${propertyIds?.join(',') ?? 'all'}_$timeRange';
    
    // Try to get from cache first (unless force refresh)
    if (!forceRefresh) {
      final cachedData = await _getCachedData<DashboardOverview>(
        cacheKey,
        (json) => DashboardOverview.fromJson(json),
      );
      if (cachedData != null) {
        return ApiResponse<DashboardOverview>(
          success: true,
          data: cachedData,
          message: 'Data loaded from cache',
          timestamp: DateTime.now().toIso8601String(),
        );
      }
    }

    try {
      // Fetch from API
      final response = await _dashboardService.getDashboardOverview(
        propertyIds: propertyIds?.join(','),
        timeRange: timeRange,
      );

      // Cache successful response
      if (response.success && response.data != null) {
        await _cacheData(cacheKey, response.data!.toJson());
      }

      return response;
    } catch (e) {
      // Try to return cached data as fallback
      final cachedData = await _getCachedData<DashboardOverview>(
        cacheKey,
        (json) => DashboardOverview.fromJson(json),
        ignoreExpiry: true,
      );
      
      if (cachedData != null) {
        return ApiResponse<DashboardOverview>(
          success: true,
          data: cachedData,
          message: 'Offline data (cached)',
          timestamp: DateTime.now().toIso8601String(),
        );
      }

      return ApiResponse<DashboardOverview>(
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to load dashboard data: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get Dashboard Alerts with caching
  Future<ApiResponse<PaginatedResponse<Alert>>> getDashboardAlerts({
    int page = 1,
    int limit = 20,
    String? severity,
    String? status,
    String? propertyId,
    bool forceRefresh = false,
  }) async {
    final cacheKey = '${_cacheKeyPrefix}alerts_${page}_${limit}_${severity ?? ''}_${status ?? ''}_${propertyId ?? ''}';
    
    // Try to get from cache first (unless force refresh)
    if (!forceRefresh) {
      final cachedData = await _getCachedData<PaginatedResponse<Alert>>(
        cacheKey,
        (json) => PaginatedResponse<Alert>.fromJson(
          json,
          (alertJson) => Alert.fromJson(alertJson as Map<String, dynamic>),
        ),
      );
      if (cachedData != null) {
        return ApiResponse<PaginatedResponse<Alert>>(
          success: true,
          data: cachedData,
          message: 'Data loaded from cache',
          timestamp: DateTime.now().toIso8601String(),
        );
      }
    }

    try {
      // Fetch from API
      final response = await _dashboardService.getAlerts(
        page: page,
        limit: limit,
        severity: severity,
        status: status,
        propertyId: propertyId,
      );

      // Cache successful response
      if (response.success && response.data != null) {
        await _cacheData(cacheKey, response.data!.toJson((alert) => alert.toJson()));
      }

      return response;
    } catch (e) {
      // Try to return cached data as fallback
      final cachedData = await _getCachedData<PaginatedResponse<Alert>>(
        cacheKey,
        (json) => PaginatedResponse<Alert>.fromJson(
          json,
          (alertJson) => Alert.fromJson(alertJson as Map<String, dynamic>),
        ),
        ignoreExpiry: true,
      );
      
      if (cachedData != null) {
        return ApiResponse<PaginatedResponse<Alert>>(
          success: true,
          data: cachedData,
          message: 'Offline data (cached)',
          timestamp: DateTime.now().toIso8601String(),
        );
      }

      return ApiResponse<PaginatedResponse<Alert>>(
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to load alerts: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Refresh all cached data
  Future<void> refreshAllData({
    List<String>? propertyIds,
    String timeRange = '24h',
  }) async {
    await Future.wait([
      getDashboardOverview(
        propertyIds: propertyIds,
        timeRange: timeRange,
        forceRefresh: true,
      ),
      getDashboardAlerts(forceRefresh: true),
    ]);
  }

  // Clear all cached data
  Future<void> clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys().where((key) => key.startsWith(_cacheKeyPrefix));
    for (final key in keys) {
      await prefs.remove(key);
    }
  }

  // Private helper methods
  Future<T?> _getCachedData<T>(
    String key,
    T Function(Map<String, dynamic>) fromJson, {
    bool ignoreExpiry = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedString = prefs.getString(key);
      
      if (cachedString == null) return null;
      
      final cachedData = jsonDecode(cachedString);
      final timestamp = DateTime.parse(cachedData['timestamp']);
      
      // Check if cache is expired (unless ignoring expiry)
      if (!ignoreExpiry && DateTime.now().difference(timestamp) > _cacheExpiry) {
        await prefs.remove(key);
        return null;
      }
      
      return fromJson(cachedData['data']);
    } catch (e) {
      // If there's any error with cached data, remove it
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
      return null;
    }
  }

  Future<void> _cacheData(String key, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      };
      await prefs.setString(key, jsonEncode(cacheData));
    } catch (e) {
      // Silently fail if caching doesn't work
    }
  }
}


