openapi: 3.0.3
info:
  title: SRSR Property Management API
  description: |
    Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities.
    
    ## Features
    - **Role-Based Access Control (RBAC)** with 6 user roles
    - **Real-time updates** via WebSocket connections
    - **Multi-property management** for residential, office, and construction sites
    - **Comprehensive system monitoring** (Water, Electricity, Security, Internet, OTT, Maintenance)
    - **Office and attendance management** with multiple locations
    - **Construction site tracking** with worker management
    - **Advanced reporting and analytics**
    - **Mobile-first design** with offline support
    
    ## Authentication
    Uses JWT tokens with access/refresh token pattern. Include Bearer token in Authorization header.
    
    ## Rate Limiting
    - Authentication: 5 req/min
    - Read operations: 100 req/min  
    - Write operations: 30 req/min
    

  version: 1.0.0
  contact:
    name: SRSR Property Management
    email: <EMAIL>
    url: https://srsrproperty.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://srsrproperty.com/terms

servers:
  - url: https://api.srsrproperty.com/v1
    description: Production server
  - url: https://staging-api.srsrproperty.com/v1
    description: Staging server
  - url: http://localhost:3000/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management and RBAC
  - name: Properties
    description: Property management and system monitoring
  - name: Dashboard
    description: Dashboard data and analytics
  - name: Office Management
    description: Office locations and attendance management
  - name: Construction Sites
    description: Construction site management and worker tracking
  - name: Maintenance
    description: Maintenance issues and work orders
  - name: Security
    description: Security systems and monitoring
  - name: Reports
    description: Report generation and analytics
  - name: Notifications
    description: Push notifications and alerts
  - name: WebSocket
    description: Real-time updates via WebSocket
  - name: System
    description: System health and monitoring

paths:
  # Authentication Endpoints
  /auth/login:
    post:
      tags: [Authentication]
      summary: User login
      description: Authenticate user and return JWT token with user permissions
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            examples:
              superAdmin:
                summary: Super Admin Login
                value:
                  email: <EMAIL>
                  password: admin123
                  deviceId: device-123
                  deviceName: iPhone 15 Pro
              propertyManager:
                summary: Property Manager Login
                value:
                  email: <EMAIL>
                  password: manager123
                  deviceId: device-456
                  deviceName: Samsung Galaxy S24
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '429':
          $ref: '#/components/responses/RateLimitError'

  /auth/refresh:
    post:
      tags: [Authentication]
      summary: Refresh access token
      description: Refresh JWT token using refresh token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refreshToken:
                  type: string
                  description: Valid refresh token
              required: [refreshToken]
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthToken'
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /auth/logout:
    post:
      tags: [Authentication]
      summary: User logout
      description: Invalidate user session and tokens
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Logout successful

  # User Management
  /users/profile:
    get:
      tags: [Users]
      summary: Get current user profile
      description: Retrieve current authenticated user's profile information
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
    
    put:
      tags: [Users]
      summary: Update user profile
      description: Update current user's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequestError'

  # Properties Management
  /properties:
    get:
      tags: [Properties]
      summary: List properties
      description: Get paginated list of properties with filtering and RBAC
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - name: type
          in: query
          description: Filter by property type
          schema:
            $ref: '#/components/schemas/PropertyType'
        - name: status
          in: query
          description: Filter by property status
          schema:
            $ref: '#/components/schemas/SystemStatus'
        - name: search
          in: query
          description: Search by property name or address
          schema:
            type: string
      responses:
        '200':
          description: Properties retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPropertiesResponse'

    post:
      tags: [Properties]
      summary: Create new property
      description: Create a new property (Admin/Property Manager only)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePropertyRequest'
      responses:
        '201':
          description: Property created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Property'
        '403':
          $ref: '#/components/responses/ForbiddenError'

  /properties/{propertyId}:
    get:
      tags: [Properties]
      summary: Get property details
      description: Retrieve detailed property information including system statuses
      parameters:
        - $ref: '#/components/parameters/PropertyIdParam'
      responses:
        '200':
          description: Property retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyDetail'
        '404':
          $ref: '#/components/responses/NotFoundError'

  /properties/{propertyId}/systems:
    get:
      tags: [Properties]
      summary: Get property system statuses
      description: Retrieve all system statuses for a property
      parameters:
        - $ref: '#/components/parameters/PropertyIdParam'
        - name: systemType
          in: query
          description: Filter by system type
          schema:
            $ref: '#/components/schemas/SystemType'
      responses:
        '200':
          description: System statuses retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemStatusSummary'

  /properties/{propertyId}/systems/{systemType}:
    get:
      tags: [Properties]
      summary: Get specific system details
      description: Retrieve detailed information for a specific system type
      parameters:
        - $ref: '#/components/parameters/PropertyIdParam'
        - name: systemType
          in: path
          required: true
          schema:
            $ref: '#/components/schemas/SystemType'
      responses:
        '200':
          description: System details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemDetail'

    put:
      tags: [Properties]
      summary: Update system status
      description: Update system status and metadata
      parameters:
        - $ref: '#/components/parameters/PropertyIdParam'
        - name: systemType
          in: path
          required: true
          schema:
            $ref: '#/components/schemas/SystemType'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSystemRequest'
      responses:
        '200':
          description: System updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemDetail'

  # Dashboard
  /dashboard/overview:
    get:
      tags: [Dashboard]
      summary: Get dashboard overview
      description: Retrieve comprehensive dashboard data with system health and statistics
      parameters:
        - name: propertyIds
          in: query
          description: Filter by specific properties (comma-separated)
          schema:
            type: string
        - name: timeRange
          in: query
          description: Time range for statistics
          schema:
            type: string
            enum: [24h, 7d, 30d, 90d]
            default: 24h
      responses:
        '200':
          description: Dashboard overview retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardOverview'

  /dashboard/alerts:
    get:
      tags: [Dashboard]
      summary: Get critical alerts
      description: Retrieve critical alerts and notifications
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - name: severity
          in: query
          description: Filter by alert severity
          schema:
            type: string
            enum: [low, medium, high, critical]
        - name: status
          in: query
          description: Filter by alert status
          schema:
            type: string
            enum: [open, acknowledged, resolved]
      responses:
        '200':
          description: Alerts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAlertsResponse'

  # Office Management
  /offices:
    get:
      tags: [Office Management]
      summary: List offices
      description: Get list of office locations and construction sites
      parameters:
        - name: type
          in: query
          description: Filter by office type
          schema:
            type: string
            enum: [office, construction_site]
      responses:
        '200':
          description: Offices retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Office'

  /offices/{officeId}/attendance:
    get:
      tags: [Office Management]
      summary: Get attendance records
      description: Retrieve attendance records for an office
      parameters:
        - name: officeId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: date
          in: query
          description: Specific date (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: startDate
          in: query
          description: Start date for range query
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: End date for range query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Attendance records retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AttendanceResponse'

    post:
      tags: [Office Management]
      summary: Submit attendance
      description: Submit attendance records for employees/workers
      parameters:
        - name: officeId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitAttendanceRequest'
      responses:
        '201':
          description: Attendance submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AttendanceSubmissionResponse'

  # System Health
  /health:
    get:
      tags: [System]
      summary: Basic health check
      description: Basic API health check
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time

  /health/detailed:
    get:
      tags: [System]
      summary: Detailed health check
      description: Comprehensive system health status
      responses:
        '200':
          description: Detailed system health
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedHealthResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication. Include in Authorization header as 'Bearer {token}'

  parameters:
    PageParam:
      name: page
      in: query
      description: Page number for pagination
      schema:
        type: integer
        minimum: 1
        default: 1

    LimitParam:
      name: limit
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20

    PropertyIdParam:
      name: propertyId
      in: path
      required: true
      description: Property ID
      schema:
        type: string
        format: uuid

  responses:
    UnauthorizedError:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: UNAUTHORIZED
            message: Authentication required
            timestamp: '2024-01-15T10:30:00Z'
            path: /properties

    ForbiddenError:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: FORBIDDEN
            message: Insufficient permissions for this operation
            timestamp: '2024-01-15T10:30:00Z'
            path: /properties

    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: NOT_FOUND
            message: Resource not found
            timestamp: '2024-01-15T10:30:00Z'
            path: /properties/123

    BadRequestError:
      description: Invalid request data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: VALIDATION_ERROR
            message: Invalid input data
            timestamp: '2024-01-15T10:30:00Z'
            path: /properties
            details:
              field: name
              issue: required

    RateLimitError:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: RATE_LIMIT_EXCEEDED
            message: Too many requests. Please try again later.
            timestamp: '2024-01-15T10:30:00Z'
            path: /auth/login
            retryAfter: 60

  schemas:
    # Authentication Schemas
    LoginRequest:
      type: object
      required: [email, password]
      properties:
        email:
          type: string
          format: email
          description: User email address
          example: <EMAIL>
        password:
          type: string
          minLength: 6
          description: User password
          example: admin123
        deviceId:
          type: string
          description: Device identifier for tracking
          example: device-123
        deviceName:
          type: string
          description: Human-readable device name
          example: iPhone 15 Pro

    LoginResponse:
      type: object
      required: [user, token, message]
      properties:
        user:
          $ref: '#/components/schemas/User'
        token:
          $ref: '#/components/schemas/AuthToken'
        message:
          type: string
          example: Login successful

    AuthToken:
      type: object
      required: [accessToken, refreshToken, expiresAt, tokenType]
      properties:
        accessToken:
          type: string
          description: JWT access token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        refreshToken:
          type: string
          description: JWT refresh token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        expiresAt:
          type: string
          format: date-time
          description: Token expiration timestamp
          example: '2024-12-31T23:59:59Z'
        tokenType:
          type: string
          enum: [Bearer]
          default: Bearer

    # User Schemas
    User:
      type: object
      required: [id, name, email, phone, role, assignedProperties, isActive, createdAt, lastLogin, permissions]
      properties:
        id:
          type: string
          format: uuid
          description: Unique user identifier
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: Full name of the user
          example: John Doe
        email:
          type: string
          format: email
          description: Email address
          example: <EMAIL>
        phone:
          type: string
          pattern: '^\\+?[1-9]\\d{1,14}$'
          description: Phone number in E.164 format
          example: '+************'
        role:
          $ref: '#/components/schemas/UserRole'
        assignedProperties:
          type: array
          items:
            type: string
            format: uuid
          description: List of property IDs assigned to user
          example: [prop-1, prop-2]
        isActive:
          type: boolean
          description: Whether the user account is active
          example: true
        createdAt:
          type: string
          format: date-time
          description: Account creation timestamp
        lastLogin:
          type: string
          format: date-time
          description: Last login timestamp
        permissions:
          $ref: '#/components/schemas/UserPermissions'
        avatar:
          type: string
          format: uri
          description: URL to user's avatar image
        timezone:
          type: string
          description: User's timezone (IANA format)
          example: Asia/Kolkata
        language:
          type: string
          enum: [en, hi, te]
          default: en
          description: Preferred language

    UserRole:
      type: string
      enum:
        - super_admin
        - property_manager
        - office_manager
        - security_personnel
        - maintenance_staff
        - construction_supervisor
      description: User role determining access permissions

    UserPermissions:
      type: object
      required: [canViewDashboard, canManageProperties, canManageOffice, canManageSecurity, canManageMaintenance, canManageUsers, canViewReports, canExportData, allowedScreens, allowedActions]
      properties:
        canViewDashboard:
          type: boolean
          example: true
        canManageProperties:
          type: boolean
          example: true
        canManageOffice:
          type: boolean
          example: true
        canManageSecurity:
          type: boolean
          example: true
        canManageMaintenance:
          type: boolean
          example: true
        canManageUsers:
          type: boolean
          example: true
        canViewReports:
          type: boolean
          example: true
        canExportData:
          type: boolean
          example: true
        allowedScreens:
          type: array
          items:
            type: string
          description: List of screens user can access
          example: [dashboard, properties, office_management, settings]
        allowedActions:
          type: array
          items:
            type: string
            enum: [create, read, update, delete, export, import]
          description: List of actions user can perform
          example: [create, read, update, delete, export]

    # Property Schemas
    PropertyType:
      type: string
      enum: [residential, office, construction]
      description: Type of property

    SystemType:
      type: string
      enum: [water, electricity, security, internet, ott, maintenance]
      description: Type of system

    SystemStatus:
      type: string
      enum: [operational, warning, critical, offline]
      description: System operational status

    Property:
      type: object
      required: [id, name, type, address, description, isActive, createdAt, updatedAt]
      properties:
        id:
          type: string
          format: uuid
          description: Unique property identifier
        name:
          type: string
          description: Property name
          example: Jublee Hills Home
        type:
          $ref: '#/components/schemas/PropertyType'
        address:
          type: string
          description: Property address
          example: Road No. 36, Jubilee Hills, Hyderabad
        description:
          type: string
          description: Property description
        isActive:
          type: boolean
          description: Whether property is active
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        coordinates:
          $ref: '#/components/schemas/Coordinates'
        images:
          type: array
          items:
            type: string
            format: uri
          description: Property images

    Coordinates:
      type: object
      required: [latitude, longitude]
      properties:
        latitude:
          type: number
          format: double
          minimum: -90
          maximum: 90
          example: 17.4065
        longitude:
          type: number
          format: double
          minimum: -180
          maximum: 180
          example: 78.4772

    # Error Response
    ErrorResponse:
      type: object
      required: [error, message, timestamp]
      properties:
        error:
          type: string
          description: Error code
          example: VALIDATION_ERROR
        message:
          type: string
          description: Human-readable error message
          example: Invalid input data
        timestamp:
          type: string
          format: date-time
          example: '2024-01-15T10:30:00Z'
        path:
          type: string
          description: API endpoint that caused the error
          example: /properties
        details:
          type: object
          additionalProperties: true
          description: Additional error details

    # Placeholder schemas (to be expanded)
    PropertyDetail:
      type: object
      description: Detailed property information with systems and activities

    SystemStatusSummary:
      type: object
      description: Summary of system status

    SystemDetail:
      type: object
      description: Detailed system information

    DashboardOverview:
      type: object
      description: Dashboard overview data

    Office:
      type: object
      description: Office information

    AttendanceResponse:
      type: object
      description: Attendance records response

    AttendanceSubmissionResponse:
      type: object
      description: Attendance submission result

    PaginatedPropertiesResponse:
      type: object
      description: Paginated properties response

    PaginatedAlertsResponse:
      type: object
      description: Paginated alerts response

    CreatePropertyRequest:
      type: object
      description: Request to create new property

    UpdateUserRequest:
      type: object
      description: Request to update user information

    UpdateSystemRequest:
      type: object
      description: Request to update system status

    SubmitAttendanceRequest:
      type: object
      description: Request to submit attendance records

    DetailedHealthResponse:
      type: object
      description: Detailed system health information
