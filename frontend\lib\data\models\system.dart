import 'package:json_annotation/json_annotation.dart';

part 'system.g.dart';

// System Status Model
@JsonSerializable()
class SystemStatus {
  final String id;
  final String propertyId;
  final String systemType;
  final String status;
  final int healthScore;
  final String? description;
  final String lastChecked;
  final Map<String, dynamic>? metadata;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const SystemStatus({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.status,
    required this.healthScore,
    this.description,
    required this.lastChecked,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory SystemStatus.fromJson(Map<String, dynamic> json) => _$SystemStatusFromJson(json);
  Map<String, dynamic> toJson() => _$SystemStatusToJson(this);

  // Helper getters
  bool get isOperational => status == 'OPERATIONAL';
  bool get isWarning => status == 'WARNING';
  bool get isCritical => status == 'CRITICAL';
  bool get isOffline => status == 'OFFLINE';

  DateTime get lastCheckedDateTime => DateTime.parse(lastChecked);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  String get propertyName => property?.name ?? 'Unknown Property';
}

// Water System Model
@JsonSerializable()
class WaterSystem {
  final String id;
  final String propertyId;
  final String tankName;
  final double capacity;
  final double currentLevel;
  final double levelPercentage;
  final String pumpStatus;
  final double? flowRate;
  final double? pressure;
  final String? quality;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final bool isActive;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const WaterSystem({
    required this.id,
    required this.propertyId,
    required this.tankName,
    required this.capacity,
    required this.currentLevel,
    required this.levelPercentage,
    required this.pumpStatus,
    this.flowRate,
    this.pressure,
    this.quality,
    this.lastMaintenance,
    this.nextMaintenance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory WaterSystem.fromJson(Map<String, dynamic> json) => _$WaterSystemFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemToJson(this);

  // Helper getters
  bool get isPumpOn => pumpStatus == 'ON';
  bool get isPumpOff => pumpStatus == 'OFF';
  bool get isPumpMaintenance => pumpStatus == 'MAINTENANCE';

  bool get isLevelCritical => levelPercentage < 10;
  bool get isLevelWarning => levelPercentage < 30 && levelPercentage >= 10;
  bool get isLevelNormal => levelPercentage >= 30;

  bool get isQualityGood => quality == 'GOOD';
  bool get isQualityFair => quality == 'FAIR';
  bool get isQualityPoor => quality == 'POOR';

  DateTime? get lastMaintenanceDateTime => lastMaintenance != null ? DateTime.parse(lastMaintenance!) : null;
  DateTime? get nextMaintenanceDateTime => nextMaintenance != null ? DateTime.parse(nextMaintenance!) : null;

  bool get needsMaintenance => nextMaintenanceDateTime != null && nextMaintenanceDateTime!.isBefore(DateTime.now());
}

// Electricity System Model
@JsonSerializable()
class ElectricitySystem {
  final String id;
  final String propertyId;
  final String systemName;
  final String generatorStatus;
  final double? fuelLevel;
  final double? powerConsumption;
  final double? voltage;
  final double? frequency;
  final double? loadPercentage;
  final String mainsPowerStatus;
  final double? batteryBackup;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final bool isActive;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const ElectricitySystem({
    required this.id,
    required this.propertyId,
    required this.systemName,
    required this.generatorStatus,
    this.fuelLevel,
    this.powerConsumption,
    this.voltage,
    this.frequency,
    this.loadPercentage,
    required this.mainsPowerStatus,
    this.batteryBackup,
    this.lastMaintenance,
    this.nextMaintenance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory ElectricitySystem.fromJson(Map<String, dynamic> json) => _$ElectricitySystemFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemToJson(this);

  // Helper getters
  bool get isGeneratorOn => generatorStatus == 'ON';
  bool get isGeneratorOff => generatorStatus == 'OFF';
  bool get isGeneratorMaintenance => generatorStatus == 'MAINTENANCE';
  bool get isGeneratorStandby => generatorStatus == 'STANDBY';

  bool get isMainsPowerAvailable => mainsPowerStatus == 'AVAILABLE';
  bool get isMainsPowerUnavailable => mainsPowerStatus == 'UNAVAILABLE';

  bool get isFuelCritical => fuelLevel != null && fuelLevel! < 10;
  bool get isFuelWarning => fuelLevel != null && fuelLevel! < 30 && fuelLevel! >= 10;
  bool get isFuelNormal => fuelLevel != null && fuelLevel! >= 30;

  bool get isBatteryCritical => batteryBackup != null && batteryBackup! < 20;
  bool get isBatteryWarning => batteryBackup != null && batteryBackup! < 50 && batteryBackup! >= 20;
  bool get isBatteryNormal => batteryBackup != null && batteryBackup! >= 50;

  DateTime? get lastMaintenanceDateTime => lastMaintenance != null ? DateTime.parse(lastMaintenance!) : null;
  DateTime? get nextMaintenanceDateTime => nextMaintenance != null ? DateTime.parse(nextMaintenance!) : null;

  bool get needsMaintenance => nextMaintenanceDateTime != null && nextMaintenanceDateTime!.isBefore(DateTime.now());
}

// Security System Model
@JsonSerializable()
class SecuritySystem {
  final String id;
  final String propertyId;
  final String systemName;
  final int cameraCount;
  final int activeCameras;
  final int accessPoints;
  final int activeAccess;
  final String alarmStatus;
  final bool motionDetected;
  final String? lastIncident;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final bool isActive;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;
  final List<SecurityCamera> cameras;

  const SecuritySystem({
    required this.id,
    required this.propertyId,
    required this.systemName,
    required this.cameraCount,
    required this.activeCameras,
    required this.accessPoints,
    required this.activeAccess,
    required this.alarmStatus,
    required this.motionDetected,
    this.lastIncident,
    this.lastMaintenance,
    this.nextMaintenance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.property,
    required this.cameras,
  });

  factory SecuritySystem.fromJson(Map<String, dynamic> json) => _$SecuritySystemFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemToJson(this);

  // Helper getters
  bool get isAlarmArmed => alarmStatus == 'ARMED';
  bool get isAlarmDisarmed => alarmStatus == 'DISARMED';
  bool get isAlarmTriggered => alarmStatus == 'TRIGGERED';
  bool get isAlarmMaintenance => alarmStatus == 'MAINTENANCE';

  double get cameraOperationalRate => cameraCount > 0 ? (activeCameras / cameraCount) * 100 : 0;
  double get accessOperationalRate => accessPoints > 0 ? (activeAccess / accessPoints) * 100 : 0;

  bool get hasCameraIssues => activeCameras < cameraCount;
  bool get hasAccessIssues => activeAccess < accessPoints;

  DateTime? get lastIncidentDateTime => lastIncident != null ? DateTime.parse(lastIncident!) : null;
  DateTime? get lastMaintenanceDateTime => lastMaintenance != null ? DateTime.parse(lastMaintenance!) : null;
  DateTime? get nextMaintenanceDateTime => nextMaintenance != null ? DateTime.parse(nextMaintenance!) : null;

  bool get needsMaintenance => nextMaintenanceDateTime != null && nextMaintenanceDateTime!.isBefore(DateTime.now());
}

// Security Camera Model
@JsonSerializable()
class SecurityCamera {
  final String id;
  final String securitySystemId;
  final String cameraName;
  final String location;
  final String status;
  final String? ipAddress;
  final String recordingStatus;
  final String? lastPing;
  final String createdAt;
  final String updatedAt;

  const SecurityCamera({
    required this.id,
    required this.securitySystemId,
    required this.cameraName,
    required this.location,
    required this.status,
    this.ipAddress,
    required this.recordingStatus,
    this.lastPing,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SecurityCamera.fromJson(Map<String, dynamic> json) => _$SecurityCameraFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityCameraToJson(this);

  // Helper getters
  bool get isOnline => status == 'ONLINE';
  bool get isOffline => status == 'OFFLINE';
  bool get isMaintenance => status == 'MAINTENANCE';

  bool get isRecording => recordingStatus == 'RECORDING';
  bool get isRecordingStopped => recordingStatus == 'STOPPED';
  bool get isRecordingError => recordingStatus == 'ERROR';

  DateTime? get lastPingDateTime => lastPing != null ? DateTime.parse(lastPing!) : null;
}

// Response Models
@JsonSerializable()
class WaterSystemsResponse {
  final List<WaterSystem> systems;
  final WaterSystemsSummary summary;

  const WaterSystemsResponse({
    required this.systems,
    required this.summary,
  });

  factory WaterSystemsResponse.fromJson(Map<String, dynamic> json) => _$WaterSystemsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemsResponseToJson(this);

  factory WaterSystemsResponse.empty() {
    return const WaterSystemsResponse(
      systems: [],
      summary: WaterSystemsSummary(
        totalSystems: 0,
        totalCapacity: 0,
        totalCurrentLevel: 0,
        averageLevel: 0,
        activePumps: 0,
        maintenanceRequired: 0,
      ),
    );
  }
}

@JsonSerializable()
class WaterSystemsSummary {
  final int totalSystems;
  final double totalCapacity;
  final double totalCurrentLevel;
  final int averageLevel;
  final int activePumps;
  final int maintenanceRequired;

  const WaterSystemsSummary({
    required this.totalSystems,
    required this.totalCapacity,
    required this.totalCurrentLevel,
    required this.averageLevel,
    required this.activePumps,
    required this.maintenanceRequired,
  });

  factory WaterSystemsSummary.fromJson(Map<String, dynamic> json) => _$WaterSystemsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemsSummaryToJson(this);
}

@JsonSerializable()
class ElectricitySystemsResponse {
  final List<ElectricitySystem> systems;
  final ElectricitySystemsSummary summary;

  const ElectricitySystemsResponse({
    required this.systems,
    required this.summary,
  });

  factory ElectricitySystemsResponse.fromJson(Map<String, dynamic> json) => _$ElectricitySystemsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemsResponseToJson(this);

  factory ElectricitySystemsResponse.empty() {
    return const ElectricitySystemsResponse(
      systems: [],
      summary: ElectricitySystemsSummary(
        totalSystems: 0,
        totalPowerConsumption: 0,
        averageFuelLevel: 0,
        activeGenerators: 0,
        mainsAvailable: 0,
        maintenanceRequired: 0,
      ),
    );
  }
}

@JsonSerializable()
class ElectricitySystemsSummary {
  final int totalSystems;
  final double totalPowerConsumption;
  final int averageFuelLevel;
  final int activeGenerators;
  final int mainsAvailable;
  final int maintenanceRequired;

  const ElectricitySystemsSummary({
    required this.totalSystems,
    required this.totalPowerConsumption,
    required this.averageFuelLevel,
    required this.activeGenerators,
    required this.mainsAvailable,
    required this.maintenanceRequired,
  });

  factory ElectricitySystemsSummary.fromJson(Map<String, dynamic> json) => _$ElectricitySystemsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemsSummaryToJson(this);
}

@JsonSerializable()
class SecuritySystemsResponse {
  final List<SecuritySystem> systems;
  final SecuritySystemsSummary summary;

  const SecuritySystemsResponse({
    required this.systems,
    required this.summary,
  });

  factory SecuritySystemsResponse.fromJson(Map<String, dynamic> json) => _$SecuritySystemsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemsResponseToJson(this);

  factory SecuritySystemsResponse.empty() {
    return const SecuritySystemsResponse(
      systems: [],
      summary: SecuritySystemsSummary(
        totalSystems: 0,
        totalCameras: 0,
        totalActiveCameras: 0,
        totalAccessPoints: 0,
        totalActiveAccess: 0,
        armedSystems: 0,
        triggeredAlarms: 0,
        maintenanceRequired: 0,
        cameraOperationalRate: 0,
        accessOperationalRate: 0,
      ),
    );
  }
}

@JsonSerializable()
class SecuritySystemsSummary {
  final int totalSystems;
  final int totalCameras;
  final int totalActiveCameras;
  final int totalAccessPoints;
  final int totalActiveAccess;
  final int armedSystems;
  final int triggeredAlarms;
  final int maintenanceRequired;
  final int cameraOperationalRate;
  final int accessOperationalRate;

  const SecuritySystemsSummary({
    required this.totalSystems,
    required this.totalCameras,
    required this.totalActiveCameras,
    required this.totalAccessPoints,
    required this.totalActiveAccess,
    required this.armedSystems,
    required this.triggeredAlarms,
    required this.maintenanceRequired,
    required this.cameraOperationalRate,
    required this.accessOperationalRate,
  });

  factory SecuritySystemsSummary.fromJson(Map<String, dynamic> json) => _$SecuritySystemsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemsSummaryToJson(this);
}

// Property Info Model (shared)
@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String? type;
  final String? address;

  const PropertyInfo({
    required this.id,
    required this.name,
    this.type,
    this.address,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}
