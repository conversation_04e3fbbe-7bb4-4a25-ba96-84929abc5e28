import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/permission_provider.dart';
import '../../widgets/permission_widgets.dart';

// Example of how to use permission-aware widgets
class PermissionExampleScreen extends ConsumerWidget {
  final String propertyId;

  const PermissionExampleScreen({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PermissionScreen(
      screenName: 'property_detail',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Property Management'),
          actions: [
            // Permission-aware action button
            PermissionActionButton(
              action: 'create',
              context: PermissionContext(propertyId: propertyId),
              onPressed: () => _showCreateDialog(context),
              child: const Icon(Icons.add),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Permission-aware card - only visible to certain roles
              PermissionCard(
                cardId: 'financial_summary',
                child: _buildFinancialSummaryCard(),
              ),

              const SizedBox(height: 16),

              // Permission-aware card with placeholder
              PermissionCard(
                cardId: 'maintenance_costs',
                showPlaceholder: true,
                child: _buildMaintenanceCostsCard(),
              ),

              const SizedBox(height: 16),

              // Permission-aware data table
              PermissionDataTable(
                tablePermission: 'view_maintenance',
                columnPermissions: [
                  'view_basic_info',
                  'view_costs',
                  'view_sensitive_data',
                  'manage_maintenance',
                ],
                columns: const [
                  DataColumn(label: Text('Task')),
                  DataColumn(label: Text('Cost')),
                  DataColumn(label: Text('Contractor')),
                  DataColumn(label: Text('Actions')),
                ],
                rows: [
                  DataRow(cells: [
                    const DataCell(Text('Plumbing Repair')),
                    const DataCell(Text('₹5,000')),
                    const DataCell(Text('ABC Plumbers')),
                    DataCell(
                      PermissionActionButton(
                        action: 'update',
                        onPressed: () => _editMaintenance(context),
                        child: const Icon(Icons.edit),
                      ),
                    ),
                  ]),
                ],
              ),

              const SizedBox(height: 16),

              // Permission-aware content filtering
              PermissionContentFilter(
                data: {
                  'propertyValue': '₹2,50,00,000',
                  'monthlyRevenue': '₹1,50,000',
                  'ownerDetails': 'Confidential',
                  'bankAccount': '****1234',
                  'description': 'Luxury residential property',
                },
                sensitiveFields: ['ownerDetails', 'bankAccount', 'propertyValue'],
                builder: (filteredData) => _buildPropertyDetails(filteredData),
              ),

              const SizedBox(height: 16),

              // Conditional widget based on multiple permissions
              PermissionBuilder(
                permission: 'export_data',
                builder: (context, canExport) {
                  return PermissionBuilder(
                    permission: 'view_reports',
                    builder: (context, canViewReports) {
                      if (canExport && canViewReports) {
                        return ElevatedButton.icon(
                          onPressed: () => _exportData(context),
                          icon: const Icon(Icons.download),
                          label: const Text('Export Report'),
                        );
                      } else if (canViewReports) {
                        return ElevatedButton.icon(
                          onPressed: () => _viewReport(context),
                          icon: const Icon(Icons.visibility),
                          label: const Text('View Report'),
                        );
                      } else {
                        return const Text(
                          'Reports not available',
                          style: TextStyle(color: Colors.grey),
                        );
                      }
                    },
                  );
                },
              ),
            ],
          ),
        ),
        drawer: PermissionDrawer(
          items: [
            DrawerItem(
              title: 'Dashboard',
              icon: const Icon(Icons.dashboard),
              permission: 'dashboard',
              onTap: () => Navigator.pushNamed(context, '/dashboard'),
            ),
            DrawerItem(
              title: 'Properties',
              icon: const Icon(Icons.home),
              permission: 'properties',
              onTap: () => Navigator.pushNamed(context, '/properties'),
            ),
            DrawerItem(
              title: 'Office Management',
              icon: const Icon(Icons.business),
              permission: 'office_management',
              onTap: () => Navigator.pushNamed(context, '/office'),
            ),
            DrawerItem(
              title: 'User Management',
              icon: const Icon(Icons.people),
              permission: 'users',
              onTap: () => Navigator.pushNamed(context, '/users'),
            ),
            DrawerItem(
              title: 'Reports',
              icon: const Icon(Icons.analytics),
              permission: 'reports',
              onTap: () => Navigator.pushNamed(context, '/reports'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Financial Summary',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text('Monthly Revenue: ₹1,50,000'),
            const Text('Monthly Expenses: ₹75,000'),
            const Text('Net Profit: ₹75,000'),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceCostsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Maintenance Costs',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text('This Month: ₹25,000'),
            const Text('Last Month: ₹30,000'),
            const Text('Average: ₹27,500'),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyDetails(Map<String, dynamic> data) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Property Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...data.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text('${entry.key}: ${entry.value}'),
            )),
          ],
        ),
      ),
    );
  }

  void _showCreateDialog(BuildContext context) {
    // Implementation for create dialog
  }

  void _editMaintenance(BuildContext context) {
    // Implementation for edit maintenance
  }

  void _exportData(BuildContext context) {
    // Implementation for data export
  }

  void _viewReport(BuildContext context) {
    // Implementation for view report
  }
}
