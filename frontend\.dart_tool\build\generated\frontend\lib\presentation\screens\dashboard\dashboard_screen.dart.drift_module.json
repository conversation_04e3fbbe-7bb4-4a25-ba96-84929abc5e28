{"elements": {}, "imports": ["package:flutter/material.dart", "package:flutter_riverpod/flutter_riverpod.dart", "package:go_router/go_router.dart", "package:fl_chart/fl_chart.dart", "package:frontend/core/theme/app_theme.dart", "package:frontend/core/services/service_locator.dart", "package:frontend/data/models/dashboard.dart", "package:frontend/data/models/alert.dart", "package:frontend/presentation/routes/app_router.dart", "package:frontend/presentation/screens/main/main_navigation_screen.dart", "package:frontend/presentation/providers/dashboard_providers.dart", "package:frontend/presentation/screens/dashboard/dashboard_v2_screen.dart"]}