import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../data/models/dashboard.dart';

class PerformanceMetrics extends StatelessWidget {
  final DashboardMetrics metrics;
  final String timeRange;

  const PerformanceMetrics({
    super.key,
    required this.metrics,
    required this.timeRange,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'Performance Metrics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getTimeRangeDisplayName(timeRange),
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Metrics Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildMetricCard(
                  context,
                  'System Uptime',
                  '${metrics.uptime.toStringAsFixed(1)}%',
                  Icons.trending_up,
                  _getUptimeColor(metrics.uptime),
                  'Overall system availability',
                ),
                _buildMetricCard(
                  context,
                  'Total Incidents',
                  metrics.incidents.toString(),
                  Icons.warning_outlined,
                  metrics.incidents > 0 ? AppTheme.warningColor : AppTheme.successColor,
                  'Issues reported',
                ),
                _buildMetricCard(
                  context,
                  'Resolved Issues',
                  metrics.resolved.toString(),
                  Icons.check_circle_outline,
                  AppTheme.successColor,
                  'Successfully resolved',
                ),
                _buildMetricCard(
                  context,
                  'Resolution Rate',
                  '${metrics.resolutionRate.toStringAsFixed(1)}%',
                  Icons.speed,
                  _getResolutionRateColor(metrics.resolutionRate),
                  'Issues resolved vs total',
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Additional Metrics Row
            Row(
              children: [
                Expanded(
                  child: _buildCompactMetric(
                    context,
                    'Open Incidents',
                    metrics.openIncidents.toString(),
                    Icons.error_outline,
                    metrics.openIncidents > 0 ? AppTheme.errorColor : AppTheme.successColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildCompactMetric(
                    context,
                    'Avg Response Time',
                    '${metrics.avgResponseTime.toStringAsFixed(1)}h',
                    Icons.timer,
                    _getResponseTimeColor(metrics.avgResponseTime),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and Title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  size: 18,
                  color: color,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Value
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Subtitle
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildCompactMetric(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: color,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getUptimeColor(double uptime) {
    if (uptime >= 99.5) return AppTheme.successColor;
    if (uptime >= 95.0) return AppTheme.infoColor;
    if (uptime >= 90.0) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  Color _getResolutionRateColor(double rate) {
    if (rate >= 90.0) return AppTheme.successColor;
    if (rate >= 75.0) return AppTheme.infoColor;
    if (rate >= 50.0) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  Color _getResponseTimeColor(double responseTime) {
    if (responseTime <= 1.0) return AppTheme.successColor;
    if (responseTime <= 4.0) return AppTheme.infoColor;
    if (responseTime <= 8.0) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  String _getTimeRangeDisplayName(String timeRange) {
    switch (timeRange) {
      case '24h':
        return 'Last 24 Hours';
      case '7d':
        return 'Last 7 Days';
      case '30d':
        return 'Last 30 Days';
      case '90d':
        return 'Last 90 Days';
      default:
        return timeRange;
    }
  }
}
