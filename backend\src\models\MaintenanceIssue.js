const mongoose = require('mongoose');

const maintenanceIssueSchema = new mongoose.Schema({
  propertyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  status: {
    type: String,
    enum: ['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'],
    default: 'OPEN',
    index: true
  },
  priority: {
    type: String,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
    required: true,
    index: true
  },
  department: {
    type: String,
    enum: ['ELECTRICITY', 'WATER', 'SECURITY', 'INTERNET', 'GENERAL'],
    required: true,
    index: true
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  reportedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reportedDate: {
    type: Date,
    default: Date.now,
    index: true
  },
  resolvedDate: {
    type: Date,
    default: null
  },
  estimatedCost: {
    type: Number,
    min: 0,
    default: null
  },
  actualCost: {
    type: Number,
    min: 0,
    default: null
  },
  recurrence: {
    type: String,
    enum: ['NONE', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'],
    default: 'NONE'
  },
  attachments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MaintenanceAttachment'
  }],
  comments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MaintenanceComment'
  }],
  activities: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MaintenanceActivity'
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
maintenanceIssueSchema.index({ propertyId: 1, status: 1 });
maintenanceIssueSchema.index({ propertyId: 1, priority: 1 });
maintenanceIssueSchema.index({ propertyId: 1, department: 1 });
maintenanceIssueSchema.index({ reportedDate: -1 });
maintenanceIssueSchema.index({ assignedTo: 1, status: 1 });

// Text search index
maintenanceIssueSchema.index({
  title: 'text',
  description: 'text'
});

// Virtual for resolution time
maintenanceIssueSchema.virtual('resolutionTime').get(function() {
  if (this.resolvedDate && this.reportedDate) {
    return this.resolvedDate.getTime() - this.reportedDate.getTime();
  }
  return null;
});

// Virtual for resolution days
maintenanceIssueSchema.virtual('resolutionDays').get(function() {
  const resolutionTime = this.resolutionTime;
  if (resolutionTime) {
    return Math.ceil(resolutionTime / (1000 * 60 * 60 * 24));
  }
  return null;
});

// Virtual for property info
maintenanceIssueSchema.virtual('property', {
  ref: 'Property',
  localField: 'propertyId',
  foreignField: '_id',
  justOne: true
});

// Virtual for assigned user info
maintenanceIssueSchema.virtual('assignedToUser', {
  ref: 'User',
  localField: 'assignedTo',
  foreignField: '_id',
  justOne: true
});

// Virtual for reporter info
maintenanceIssueSchema.virtual('reportedByUser', {
  ref: 'User',
  localField: 'reportedBy',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware
maintenanceIssueSchema.pre('save', function(next) {
  // Auto-set resolved date when status changes to RESOLVED or CLOSED
  if (this.isModified('status') && 
      (this.status === 'RESOLVED' || this.status === 'CLOSED') && 
      !this.resolvedDate) {
    this.resolvedDate = new Date();
  }
  
  // Clear resolved date if status changes back to OPEN or IN_PROGRESS
  if (this.isModified('status') && 
      (this.status === 'OPEN' || this.status === 'IN_PROGRESS')) {
    this.resolvedDate = null;
  }
  
  next();
});

// Static methods
maintenanceIssueSchema.statics.getStatistics = async function(filter = {}) {
  const pipeline = [
    { $match: filter },
    {
      $group: {
        _id: null,
        totalIssues: { $sum: 1 },
        openIssues: {
          $sum: { $cond: [{ $eq: ['$status', 'OPEN'] }, 1, 0] }
        },
        inProgressIssues: {
          $sum: { $cond: [{ $eq: ['$status', 'IN_PROGRESS'] }, 1, 0] }
        },
        resolvedIssues: {
          $sum: { $cond: [{ $eq: ['$status', 'RESOLVED'] }, 1, 0] }
        },
        closedIssues: {
          $sum: { $cond: [{ $eq: ['$status', 'CLOSED'] }, 1, 0] }
        },
        criticalIssues: {
          $sum: { $cond: [{ $eq: ['$priority', 'CRITICAL'] }, 1, 0] }
        },
        highPriorityIssues: {
          $sum: { $cond: [{ $eq: ['$priority', 'HIGH'] }, 1, 0] }
        },
        mediumPriorityIssues: {
          $sum: { $cond: [{ $eq: ['$priority', 'MEDIUM'] }, 1, 0] }
        },
        lowPriorityIssues: {
          $sum: { $cond: [{ $eq: ['$priority', 'LOW'] }, 1, 0] }
        },
        avgResolutionTime: {
          $avg: {
            $cond: [
              { $ne: ['$resolvedDate', null] },
              {
                $divide: [
                  { $subtract: ['$resolvedDate', '$reportedDate'] },
                  1000 * 60 * 60 // Convert to hours
                ]
              },
              null
            ]
          }
        }
      }
    }
  ];

  const [stats] = await this.aggregate(pipeline);
  
  if (!stats) {
    return {
      totalIssues: 0,
      openIssues: 0,
      inProgressIssues: 0,
      resolvedIssues: 0,
      closedIssues: 0,
      criticalIssues: 0,
      highPriorityIssues: 0,
      mediumPriorityIssues: 0,
      lowPriorityIssues: 0,
      averageResolutionTime: 0,
      issuesByDepartment: {},
      issuesByProperty: {},
      trends: []
    };
  }

  // Get issues by department
  const departmentStats = await this.aggregate([
    { $match: filter },
    { $group: { _id: '$department', count: { $sum: 1 } } }
  ]);

  // Get issues by property
  const propertyStats = await this.aggregate([
    { $match: filter },
    { $group: { _id: '$propertyId', count: { $sum: 1 } } }
  ]);

  // Get trends (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const trends = await this.aggregate([
    {
      $match: {
        ...filter,
        reportedDate: { $gte: thirtyDaysAgo }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$reportedDate'
          }
        },
        created: { $sum: 1 },
        resolved: {
          $sum: {
            $cond: [
              { $ne: ['$resolvedDate', null] },
              1,
              0
            ]
          }
        }
      }
    },
    { $sort: { _id: 1 } }
  ]);

  return {
    ...stats,
    averageResolutionTime: stats.avgResolutionTime || 0,
    issuesByDepartment: departmentStats.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
    issuesByProperty: propertyStats.reduce((acc, item) => {
      acc[item._id.toString()] = item.count;
      return acc;
    }, {}),
    trends: trends.map(trend => ({
      date: trend._id,
      created: trend.created,
      resolved: trend.resolved,
      pending: trend.created - trend.resolved
    }))
  };
};

// Instance methods
maintenanceIssueSchema.methods.addActivity = async function(action, userId, oldValue = null, newValue = null) {
  const MaintenanceActivity = mongoose.model('MaintenanceActivity');
  
  const activity = new MaintenanceActivity({
    issueId: this._id,
    userId,
    action,
    oldValue,
    newValue
  });
  
  await activity.save();
  this.activities.push(activity._id);
  await this.save();
  
  return activity;
};

maintenanceIssueSchema.methods.addComment = async function(userId, comment) {
  const MaintenanceComment = mongoose.model('MaintenanceComment');
  
  const commentDoc = new MaintenanceComment({
    issueId: this._id,
    userId,
    comment
  });
  
  await commentDoc.save();
  this.comments.push(commentDoc._id);
  await this.save();
  
  return commentDoc;
};

module.exports = mongoose.model('MaintenanceIssue', maintenanceIssueSchema);
