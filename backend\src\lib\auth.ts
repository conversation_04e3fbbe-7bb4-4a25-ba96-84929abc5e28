import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { UserRole } from '@prisma/client';
import { JWTPayload, UserPermissions } from '@/types';

const JWT_SECRET = process.env.JWT_SECRET!;
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET!;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

if (!JWT_SECRET || !JWT_REFRESH_SECRET) {
  throw new Error('JWT_SECRET and JWT_REFRESH_SECRET must be defined in environment variables');
}

export class AuthService {
  static async hashPassword(password: string): Promise<string> {
    const rounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    return bcrypt.hash(password, rounds);
  }

  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  static generateAccessToken(payload: JWTPayload): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN } as jwt.SignOptions);
  }

  static generateRefreshToken(payload: JWTPayload): string {
    return jwt.sign(payload, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN } as jwt.SignOptions);
  }

  static verifyAccessToken(token: string): JWTPayload {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  }

  static verifyRefreshToken(token: string): JWTPayload {
    return jwt.verify(token, JWT_REFRESH_SECRET) as JWTPayload;
  }

  static getTokenExpiration(expiresIn: string = JWT_EXPIRES_IN): Date {
    const now = new Date();
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    
    if (!match) {
      throw new Error('Invalid expires in format');
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's':
        return new Date(now.getTime() + value * 1000);
      case 'm':
        return new Date(now.getTime() + value * 60 * 1000);
      case 'h':
        return new Date(now.getTime() + value * 60 * 60 * 1000);
      case 'd':
        return new Date(now.getTime() + value * 24 * 60 * 60 * 1000);
      default:
        throw new Error('Invalid time unit');
    }
  }

  static getUserPermissions(role: UserRole): UserPermissions {
    const basePermissions: UserPermissions = {
      canViewDashboard: false,
      canManageProperties: false,
      canManageOffice: false,
      canManageSecurity: false,
      canManageMaintenance: false,
      canManageUsers: false,
      canViewReports: false,
      canExportData: false,
      allowedScreens: [],
      allowedActions: ['read'],
    };

    switch (role) {
      case UserRole.SUPER_ADMIN:
        return {
          canViewDashboard: true,
          canManageProperties: true,
          canManageOffice: true,
          canManageSecurity: true,
          canManageMaintenance: true,
          canManageUsers: true,
          canViewReports: true,
          canExportData: true,
          allowedScreens: [
            'dashboard',
            'properties',
            'office_management',
            'security',
            'maintenance',
            'users',
            'reports',
            'settings'
          ],
          allowedActions: ['create', 'read', 'update', 'delete', 'export', 'import'],
        };

      case UserRole.PROPERTY_MANAGER:
        return {
          canViewDashboard: true,
          canManageProperties: true,
          canManageOffice: false,
          canManageSecurity: true,
          canManageMaintenance: true,
          canManageUsers: false,
          canViewReports: true,
          canExportData: true,
          allowedScreens: [
            'dashboard',
            'properties',
            'security',
            'maintenance',
            'reports'
          ],
          allowedActions: ['create', 'read', 'update', 'delete', 'export'],
        };

      case UserRole.OFFICE_MANAGER:
        return {
          canViewDashboard: true,
          canManageProperties: false,
          canManageOffice: true,
          canManageSecurity: false,
          canManageMaintenance: false,
          canManageUsers: false,
          canViewReports: true,
          canExportData: true,
          allowedScreens: [
            'dashboard',
            'office_management',
            'reports'
          ],
          allowedActions: ['create', 'read', 'update', 'export'],
        };

      case UserRole.SECURITY_PERSONNEL:
        return {
          canViewDashboard: true,
          canManageProperties: false,
          canManageOffice: false,
          canManageSecurity: true,
          canManageMaintenance: false,
          canManageUsers: false,
          canViewReports: false,
          canExportData: false,
          allowedScreens: [
            'dashboard',
            'security'
          ],
          allowedActions: ['read', 'update'],
        };

      case UserRole.MAINTENANCE_STAFF:
        return {
          canViewDashboard: true,
          canManageProperties: false,
          canManageOffice: false,
          canManageSecurity: false,
          canManageMaintenance: true,
          canManageUsers: false,
          canViewReports: false,
          canExportData: false,
          allowedScreens: [
            'dashboard',
            'maintenance'
          ],
          allowedActions: ['read', 'update'],
        };

      case UserRole.CONSTRUCTION_SUPERVISOR:
        return {
          canViewDashboard: true,
          canManageProperties: false,
          canManageOffice: true,
          canManageSecurity: false,
          canManageMaintenance: false,
          canManageUsers: false,
          canViewReports: true,
          canExportData: true,
          allowedScreens: [
            'dashboard',
            'office_management',
            'reports'
          ],
          allowedActions: ['create', 'read', 'update', 'export'],
        };

      default:
        return basePermissions;
    }
  }

  static hasPermission(
    userRole: UserRole,
    requiredPermission: keyof UserPermissions,
    requiredValue: boolean = true
  ): boolean {
    const permissions = this.getUserPermissions(userRole);
    return permissions[requiredPermission] === requiredValue;
  }

  static canAccessScreen(userRole: UserRole, screen: string): boolean {
    const permissions = this.getUserPermissions(userRole);
    return permissions.allowedScreens.includes(screen);
  }

  static canPerformAction(
    userRole: UserRole,
    action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import'
  ): boolean {
    const permissions = this.getUserPermissions(userRole);
    return permissions.allowedActions.includes(action);
  }
}
