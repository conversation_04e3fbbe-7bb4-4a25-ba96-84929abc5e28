import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/breadcrumb_permission_provider.dart';

// Tab permission models
class TabPermission {
  final bool visible;
  final bool enabled;
  final String accessLevel;
  final Map<String, dynamic>? restrictions;
  final Map<String, ComponentPermission> components;

  const TabPermission({
    required this.visible,
    required this.enabled,
    required this.accessLevel,
    this.restrictions,
    required this.components,
  });

  factory TabPermission.fromJson(Map<String, dynamic> json) {
    final componentsMap = <String, ComponentPermission>{};
    final components = json['components'] as Map<String, dynamic>? ?? {};
    
    components.forEach((key, value) {
      componentsMap[key] = ComponentPermission.fromJson(value);
    });

    return TabPermission(
      visible: json['visible'] ?? false,
      enabled: json['enabled'] ?? false,
      accessLevel: json['accessLevel'] ?? 'none',
      restrictions: json['restrictions'],
      components: componentsMap,
    );
  }

  bool get isReadOnly => accessLevel == 'read';
  bool get isRestricted => accessLevel == 'restricted';
  bool get hasFullAccess => accessLevel == 'full';
  bool get hasNoAccess => accessLevel == 'none' || !visible;

  bool isComponentHidden(String componentId) {
    final hideComponents = restrictions?['hideComponents'] as List<dynamic>?;
    return hideComponents?.contains(componentId) ?? false;
  }

  bool isComponentDisabled(String componentId) {
    final disableComponents = restrictions?['disableComponents'] as List<dynamic>?;
    return disableComponents?.contains(componentId) ?? false;
  }
}

// Enhanced path permission result with tabs
class EnhancedPathPermissionResult extends PathPermissionResult {
  final Map<String, TabPermission> tabs;

  const EnhancedPathPermissionResult({
    required super.hasAccess,
    required super.accessLevel,
    required super.permissions,
    super.restrictions,
    required super.components,
    super.reason,
    required this.tabs,
  });

  factory EnhancedPathPermissionResult.fromJson(Map<String, dynamic> json) {
    final tabsMap = <String, TabPermission>{};
    final tabs = json['tabs'] as Map<String, dynamic>? ?? {};
    
    tabs.forEach((key, value) {
      tabsMap[key] = TabPermission.fromJson(value);
    });

    final componentsMap = <String, ComponentPermission>{};
    final components = json['components'] as Map<String, dynamic>? ?? {};
    
    components.forEach((key, value) {
      componentsMap[key] = ComponentPermission.fromJson(value);
    });

    return EnhancedPathPermissionResult(
      hasAccess: json['hasAccess'] ?? false,
      accessLevel: json['accessLevel'] ?? 'none',
      permissions: List<String>.from(json['permissions'] ?? []),
      restrictions: json['restrictions'],
      components: componentsMap,
      reason: json['reason'],
      tabs: tabsMap,
    );
  }
}

// Permission-aware tab bar
class PermissionTabBar extends ConsumerWidget implements PreferredSizeWidget {
  final String screenPath;
  final Map<String, String> parameters;
  final List<TabDefinition> allTabs;
  final TabController? controller;
  final bool isScrollable;

  const PermissionTabBar({
    super.key,
    required this.screenPath,
    required this.parameters,
    required this.allTabs,
    this.controller,
    this.isScrollable = false,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionRequest = PathPermissionRequest(
      path: screenPath,
      params: parameters,
    );
    final permissionAsync = ref.watch(pathPermissionProvider(permissionRequest));

    return permissionAsync.when(
      data: (permission) {
        if (permission is! EnhancedPathPermissionResult) {
          return _buildErrorTabBar('Invalid permission data');
        }

        final visibleTabs = _getVisibleTabs(permission);
        
        if (visibleTabs.isEmpty) {
          return _buildEmptyTabBar();
        }

        return TabBar(
          controller: controller,
          isScrollable: isScrollable,
          tabs: visibleTabs.map((tabDef) {
            final tabPermission = permission.tabs[tabDef.tabId];
            return _buildTab(tabDef, tabPermission);
          }).toList(),
        );
      },
      loading: () => _buildLoadingTabBar(),
      error: (error, stack) => _buildErrorTabBar(error.toString()),
    );
  }

  List<TabDefinition> _getVisibleTabs(EnhancedPathPermissionResult permission) {
    return allTabs.where((tab) {
      final tabPermission = permission.tabs[tab.tabId];
      return tabPermission?.visible ?? false;
    }).toList();
  }

  Widget _buildTab(TabDefinition tabDef, TabPermission? tabPermission) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (tabDef.icon != null) ...[
            Icon(_getIconData(tabDef.icon!), size: 16),
            const SizedBox(width: 4),
          ],
          Text(tabDef.name),
          if (tabPermission != null) ...[
            const SizedBox(width: 4),
            _buildPermissionIndicator(tabPermission),
          ],
        ],
      ),
    );
  }

  Widget _buildPermissionIndicator(TabPermission tabPermission) {
    if (tabPermission.hasFullAccess) {
      return const SizedBox.shrink(); // No indicator for full access
    }

    Color indicatorColor;
    String tooltip;

    if (tabPermission.isReadOnly) {
      indicatorColor = Colors.orange;
      tooltip = 'Read Only';
    } else if (tabPermission.isRestricted) {
      indicatorColor = Colors.red;
      tooltip = 'Restricted Access';
    } else {
      indicatorColor = Colors.grey;
      tooltip = 'Limited Access';
    }

    return Tooltip(
      message: tooltip,
      child: Container(
        width: 6,
        height: 6,
        decoration: BoxDecoration(
          color: indicatorColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildLoadingTabBar() {
    return const TabBar(
      tabs: [
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 8),
              Text('Loading...'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorTabBar(String error) {
    return TabBar(
      tabs: [
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, size: 16, color: Colors.red),
              const SizedBox(width: 8),
              Text('Error: $error'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyTabBar() {
    return const TabBar(
      tabs: [
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.block, size: 16, color: Colors.grey),
              SizedBox(width: 8),
              Text('No accessible tabs'),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'dashboard': return Icons.dashboard;
      case 'videocam': return Icons.videocam;
      case 'security': return Icons.security;
      case 'build': return Icons.build;
      case 'contacts': return Icons.contacts;
      case 'electrical_services': return Icons.electrical_services;
      case 'battery_charging_full': return Icons.battery_charging_full;
      case 'water_drop': return Icons.water_drop;
      case 'settings': return Icons.settings;
      case 'schedule': return Icons.schedule;
      case 'people': return Icons.people;
      default: return Icons.tab;
    }
  }
}

// Permission-aware tab view
class PermissionTabView extends ConsumerWidget {
  final String screenPath;
  final Map<String, String> parameters;
  final String currentTabId;
  final Widget child;

  const PermissionTabView({
    super.key,
    required this.screenPath,
    required this.parameters,
    required this.currentTabId,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionRequest = PathPermissionRequest(
      path: screenPath,
      params: parameters,
    );
    final permissionAsync = ref.watch(pathPermissionProvider(permissionRequest));

    return permissionAsync.when(
      data: (permission) {
        if (permission is! EnhancedPathPermissionResult) {
          return _buildErrorView('Invalid permission data');
        }

        final tabPermission = permission.tabs[currentTabId];
        
        if (tabPermission == null || !tabPermission.visible) {
          return _buildNoAccessView();
        }

        return _TabPermissionProvider(
          tabPermission: tabPermission,
          child: _wrapWithAccessLevelIndicator(tabPermission, child),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorView(error.toString()),
    );
  }

  Widget _wrapWithAccessLevelIndicator(TabPermission tabPermission, Widget child) {
    if (tabPermission.hasFullAccess) {
      return child;
    }

    return Stack(
      children: [
        child,
        if (tabPermission.isReadOnly || tabPermission.isRestricted)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: tabPermission.isReadOnly ? Colors.orange : Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                tabPermission.isReadOnly ? 'Read Only' : 'Restricted',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNoAccessView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.block, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Access Denied',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          Text(
            'You don\'t have permission to access this tab.',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Tab',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          Text(
            error,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Provider widget for tab permissions
class _TabPermissionProvider extends InheritedWidget {
  final TabPermission tabPermission;

  const _TabPermissionProvider({
    required this.tabPermission,
    required super.child,
  });

  static TabPermission? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<_TabPermissionProvider>()?.tabPermission;
  }

  @override
  bool updateShouldNotify(_TabPermissionProvider oldWidget) {
    return tabPermission != oldWidget.tabPermission;
  }
}

// Permission-aware tab component
class TabComponent extends StatelessWidget {
  final String componentId;
  final Widget child;
  final Widget? restrictedWidget;
  final Widget? hiddenWidget;

  const TabComponent({
    super.key,
    required this.componentId,
    required this.child,
    this.restrictedWidget,
    this.hiddenWidget,
  });

  @override
  Widget build(BuildContext context) {
    final tabPermission = _TabPermissionProvider.of(context);
    
    if (tabPermission == null) {
      return hiddenWidget ?? const SizedBox.shrink();
    }

    // Check if component is hidden by tab restrictions
    if (tabPermission.isComponentHidden(componentId)) {
      return hiddenWidget ?? const SizedBox.shrink();
    }

    // Check component-specific permissions
    final componentPermission = tabPermission.components[componentId];
    
    if (componentPermission == null || !componentPermission.visible) {
      return hiddenWidget ?? const SizedBox.shrink();
    }

    // Check if component is disabled
    if (tabPermission.isComponentDisabled(componentId) || !componentPermission.enabled) {
      return restrictedWidget ?? _buildDisabledWrapper(child);
    }

    return child;
  }

  Widget _buildDisabledWrapper(Widget child) {
    return Opacity(
      opacity: 0.5,
      child: IgnorePointer(child: child),
    );
  }
}

// Tab definition model
class TabDefinition {
  final String tabId;
  final String name;
  final String? description;
  final int order;
  final String? icon;

  const TabDefinition({
    required this.tabId,
    required this.name,
    this.description,
    required this.order,
    this.icon,
  });
}

// Helper function to get current tab permission
TabPermission? getCurrentTabPermission(BuildContext context) {
  return _TabPermissionProvider.of(context);
}

// Helper function to check tab component permission
bool hasTabComponentPermission(BuildContext context, String componentId, String permission) {
  final tabPermission = _TabPermissionProvider.of(context);
  final componentPermission = tabPermission?.components[componentId];
  return componentPermission?.hasPermission(permission) ?? false;
}
