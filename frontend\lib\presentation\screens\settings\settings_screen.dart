import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../main/main_navigation_screen.dart';
import '../../routes/app_router.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  bool _biometricEnabled = false;
  String _selectedLanguage = 'English';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Settings',
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // User Profile Section
            _buildUserProfileSection(),
            
            const SizedBox(height: 8),
            
            // App Settings
            _buildAppSettingsSection(),
            
            const SizedBox(height: 8),
            
            // System Administration (for admin users)
            _buildSystemAdminSection(),
            
            const SizedBox(height: 8),
            
            // Support & About
            _buildSupportSection(),
            
            const SizedBox(height: 8),
            
            // Logout
            _buildLogoutSection(),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildUserProfileSection() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'User Profile',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // User Info
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'John Doe', // TODO: Get from user provider
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '<EMAIL>',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        'Super Admin', // TODO: Get from user provider
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    _showEditProfileDialog();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettingsSection() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'App Settings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Notifications
          SwitchListTile(
            title: const Text('Notifications'),
            subtitle: const Text('Receive push notifications'),
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
            },
            secondary: const Icon(Icons.notifications),
          ),
          
          const Divider(height: 1),
          
          // Dark Mode
          SwitchListTile(
            title: const Text('Dark Mode'),
            subtitle: const Text('Use dark theme'),
            value: _darkModeEnabled,
            onChanged: (value) {
              setState(() {
                _darkModeEnabled = value;
              });
              // TODO: Implement theme switching
            },
            secondary: const Icon(Icons.dark_mode),
          ),
          
          const Divider(height: 1),
          
          // Biometric Authentication
          SwitchListTile(
            title: const Text('Biometric Login'),
            subtitle: const Text('Use fingerprint or face ID'),
            value: _biometricEnabled,
            onChanged: (value) {
              setState(() {
                _biometricEnabled = value;
              });
              // TODO: Implement biometric authentication
            },
            secondary: const Icon(Icons.fingerprint),
          ),
          
          const Divider(height: 1),
          
          // Language
          ListTile(
            title: const Text('Language'),
            subtitle: Text(_selectedLanguage),
            leading: const Icon(Icons.language),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              _showLanguageDialog();
            },
          ),
          
          const Divider(height: 1),
          
          // Data & Storage
          ListTile(
            title: const Text('Data & Storage'),
            subtitle: const Text('Manage offline data and cache'),
            leading: const Icon(Icons.storage),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              _showDataStorageDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSystemAdminSection() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'System Administration',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // User Management
          ListTile(
            title: const Text('User Management'),
            subtitle: const Text('Manage users and permissions'),
            leading: const Icon(Icons.people),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Navigate to user management
            },
          ),
          
          const Divider(height: 1),
          
          // Property Management
          ListTile(
            title: const Text('Property Configuration'),
            subtitle: const Text('Configure property settings'),
            leading: const Icon(Icons.business),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Navigate to property configuration
            },
          ),
          
          const Divider(height: 1),
          
          // System Monitoring
          ListTile(
            title: const Text('System Monitoring'),
            subtitle: const Text('View system health and logs'),
            leading: const Icon(Icons.monitor_heart),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Navigate to system monitoring
            },
          ),
          
          const Divider(height: 1),
          
          // Backup & Restore
          ListTile(
            title: const Text('Backup & Restore'),
            subtitle: const Text('Manage data backups'),
            leading: const Icon(Icons.backup),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              _showBackupDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Support & About',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Help & FAQ
          ListTile(
            title: const Text('Help & FAQ'),
            subtitle: const Text('Get help and find answers'),
            leading: const Icon(Icons.help),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Navigate to help
            },
          ),
          
          const Divider(height: 1),
          
          // Contact Support
          ListTile(
            title: const Text('Contact Support'),
            subtitle: const Text('Get in touch with our team'),
            leading: const Icon(Icons.support_agent),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              _showContactSupportDialog();
            },
          ),
          
          const Divider(height: 1),
          
          // Privacy Policy
          ListTile(
            title: const Text('Privacy Policy'),
            leading: const Icon(Icons.privacy_tip),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // TODO: Show privacy policy
            },
          ),
          
          const Divider(height: 1),
          
          // About
          ListTile(
            title: const Text('About'),
            subtitle: Text('Version ${AppConstants.appVersion}'),
            leading: const Icon(Icons.info),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              _showAboutDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutSection() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListTile(
        title: Text(
          'Logout',
          style: TextStyle(
            color: Theme.of(context).colorScheme.error,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: Icon(
          Icons.logout,
          color: Theme.of(context).colorScheme.error,
        ),
        onTap: () {
          _showLogoutDialog();
        },
      ),
    );
  }

  void _showEditProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: const Text('Profile editing feature coming soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    final languages = ['English', 'Hindi', 'Telugu'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages.map((language) {
            return RadioListTile<String>(
              title: Text(language),
              value: language,
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showDataStorageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data & Storage'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Cache Size: 45.2 MB'),
            const SizedBox(height: 8),
            const Text('Offline Data: 12.8 MB'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // TODO: Clear cache
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Cache cleared successfully')),
                );
              },
              child: const Text('Clear Cache'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup & Restore'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Create Backup'),
              leading: const Icon(Icons.backup),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Create backup
              },
            ),
            ListTile(
              title: const Text('Restore from Backup'),
              leading: const Icon(Icons.restore),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Restore backup
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showContactSupportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: <EMAIL>'),
            SizedBox(height: 8),
            Text('Phone: +91 9999999999'),
            SizedBox(height: 8),
            Text('Hours: 9 AM - 6 PM (Mon-Fri)'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.business,
          color: Colors.white,
          size: 30,
        ),
      ),
      children: [
        const Text('Comprehensive property and office management solution.'),
        const SizedBox(height: 16),
        const Text('© 2025 SRSR Property Management'),
      ],
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement logout logic
              context.go(AppRoutes.login);
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
