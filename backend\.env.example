# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/srsr_property_management"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
NODE_ENV="development"
PORT=3000
API_VERSION="v1"

# CORS Configuration
CORS_ORIGIN="http://localhost:3000,http://localhost:3002"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH="./uploads"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,application/pdf"

# Email Configuration (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# WebSocket Configuration
WS_PORT=3002

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-this"

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH="/api-docs"

# Health Check
HEALTH_CHECK_INTERVAL=30000

# Default Admin User (for seeding)
DEFAULT_ADMIN_EMAIL="<EMAIL>"
DEFAULT_ADMIN_PASSWORD="admin123"
DEFAULT_ADMIN_NAME="System Administrator"
