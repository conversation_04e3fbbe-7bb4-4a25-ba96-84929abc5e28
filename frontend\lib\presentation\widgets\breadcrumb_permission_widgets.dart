import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/breadcrumb_permission_provider.dart';

// Permission-aware screen based on breadcrumb path
class BreadcrumbPermissionScreen extends ConsumerWidget {
  final String path;
  final Map<String, String>? params;
  final Widget child;
  final Widget? unauthorizedWidget;
  final Widget? loadingWidget;

  const BreadcrumbPermissionScreen({
    super.key,
    required this.path,
    this.params,
    required this.child,
    this.unauthorizedWidget,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionRequest = PathPermissionRequest(path: path, params: params);
    final permissionAsync = ref.watch(pathPermissionProvider(permissionRequest));

    return permissionAsync.when(
      data: (permission) {
        if (permission.hasAccess) {
          return _BreadcrumbPermissionProvider(
            permission: permission,
            child: child,
          );
        } else {
          return unauthorizedWidget ?? _buildUnauthorizedScreen(context, permission.reason);
        }
      },
      loading: () => loadingWidget ?? const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorScreen(context, error.toString()),
    );
  }

  Widget _buildUnauthorizedScreen(BuildContext context, String? reason) {
    return Scaffold(
      appBar: AppBar(title: const Text('Access Denied')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.lock_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'Access Denied',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              reason ?? 'You don\'t have permission to access this page.',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(BuildContext context, String error) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Error Loading Page',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.red),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Provider widget that makes permission data available to child widgets
class _BreadcrumbPermissionProvider extends InheritedWidget {
  final PathPermissionResult permission;

  const _BreadcrumbPermissionProvider({
    required this.permission,
    required super.child,
  });

  static PathPermissionResult? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<_BreadcrumbPermissionProvider>()?.permission;
  }

  @override
  bool updateShouldNotify(_BreadcrumbPermissionProvider oldWidget) {
    return permission != oldWidget.permission;
  }
}

// Permission-aware component widget
class BreadcrumbComponent extends StatelessWidget {
  final String componentId;
  final Widget child;
  final Widget? restrictedWidget;
  final Widget? hiddenWidget;

  const BreadcrumbComponent({
    super.key,
    required this.componentId,
    required this.child,
    this.restrictedWidget,
    this.hiddenWidget,
  });

  @override
  Widget build(BuildContext context) {
    final permission = _BreadcrumbPermissionProvider.of(context);
    
    if (permission == null) {
      return hiddenWidget ?? const SizedBox.shrink();
    }

    final componentPermission = permission.components[componentId];
    
    if (componentPermission == null || !componentPermission.visible) {
      return hiddenWidget ?? const SizedBox.shrink();
    }

    if (!componentPermission.enabled) {
      return restrictedWidget ?? _buildDisabledWrapper(child);
    }

    return child;
  }

  Widget _buildDisabledWrapper(Widget child) {
    return Opacity(
      opacity: 0.5,
      child: IgnorePointer(child: child),
    );
  }
}

// Permission-aware action button
class BreadcrumbActionButton extends StatelessWidget {
  final String componentId;
  final String requiredPermission;
  final VoidCallback? onPressed;
  final Widget child;

  const BreadcrumbActionButton({
    super.key,
    required this.componentId,
    required this.requiredPermission,
    required this.onPressed,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final permission = _BreadcrumbPermissionProvider.of(context);
    final componentPermission = permission?.components[componentId];

    final hasPermission = componentPermission?.hasPermission(requiredPermission) ?? false;
    final isEnabled = componentPermission?.enabled ?? false;

    return ElevatedButton(
      onPressed: (hasPermission && isEnabled) ? onPressed : null,
      child: child,
    );
  }
}

// Breadcrumb navigation widget
class BreadcrumbNavigation extends ConsumerWidget {
  final String currentPath;
  final Map<String, String>? params;
  final TextStyle? textStyle;
  final TextStyle? linkStyle;
  final String separator;

  const BreadcrumbNavigation({
    super.key,
    required this.currentPath,
    this.params,
    this.textStyle,
    this.linkStyle,
    this.separator = ' > ',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final breadcrumbRequest = BreadcrumbRequest(path: currentPath, params: params);
    final breadcrumbsAsync = ref.watch(breadcrumbNavigationProvider(breadcrumbRequest));

    return breadcrumbsAsync.when(
      data: (breadcrumbs) => _buildBreadcrumbs(context, breadcrumbs),
      loading: () => const SizedBox(height: 20, child: LinearProgressIndicator()),
      error: (error, stack) => Text('Error: $error', style: TextStyle(color: Colors.red)),
    );
  }

  Widget _buildBreadcrumbs(BuildContext context, List<BreadcrumbItem> breadcrumbs) {
    if (breadcrumbs.isEmpty) return const SizedBox.shrink();

    final widgets = <Widget>[];

    for (int i = 0; i < breadcrumbs.length; i++) {
      final breadcrumb = breadcrumbs[i];
      final isLast = i == breadcrumbs.length - 1;

      if (isLast) {
        // Current page - not clickable
        widgets.add(
          Text(
            breadcrumb.name,
            style: textStyle ?? Theme.of(context).textTheme.bodyMedium,
          ),
        );
      } else {
        // Clickable breadcrumb
        widgets.add(
          GestureDetector(
            onTap: () => _navigateTo(context, breadcrumb.path),
            child: Text(
              breadcrumb.name,
              style: linkStyle ?? 
                Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                  decoration: TextDecoration.underline,
                ),
            ),
          ),
        );
      }

      // Add separator
      if (!isLast) {
        widgets.add(
          Text(
            separator,
            style: textStyle ?? Theme.of(context).textTheme.bodyMedium,
          ),
        );
      }
    }

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      children: widgets,
    );
  }

  void _navigateTo(BuildContext context, String path) {
    // Implement navigation logic based on your routing system
    Navigator.pushNamed(context, path);
  }
}

// Permission-aware data display with field filtering
class BreadcrumbDataDisplay extends StatelessWidget {
  final String componentId;
  final Map<String, dynamic> data;
  final Widget Function(Map<String, dynamic> filteredData) builder;

  const BreadcrumbDataDisplay({
    super.key,
    required this.componentId,
    required this.data,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final permission = _BreadcrumbPermissionProvider.of(context);
    final componentPermission = permission?.components[componentId];

    if (componentPermission == null || !componentPermission.visible) {
      return const SizedBox.shrink();
    }

    // Filter data based on restrictions
    final filteredData = Map<String, dynamic>.from(data);
    final hideFields = componentPermission.restrictions?['hideFields'] as List<dynamic>?;

    if (hideFields != null) {
      for (final field in hideFields) {
        filteredData.remove(field.toString());
      }
    }

    return builder(filteredData);
  }
}

// Permission-aware form with field restrictions
class BreadcrumbForm extends StatelessWidget {
  final String componentId;
  final List<FormFieldConfig> fields;
  final Widget Function(List<FormFieldConfig> allowedFields) builder;

  const BreadcrumbForm({
    super.key,
    required this.componentId,
    required this.fields,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final permission = _BreadcrumbPermissionProvider.of(context);
    final componentPermission = permission?.components[componentId];

    if (componentPermission == null || !componentPermission.visible) {
      return const SizedBox.shrink();
    }

    // Filter fields based on restrictions
    final allowedFields = fields.where((field) {
      return !componentPermission.isFieldHidden(field.name);
    }).map((field) {
      // Disable field if component is read-only or field is specifically disabled
      final isReadOnly = permission?.accessLevel == 'read' || 
                        !componentPermission.enabled ||
                        componentPermission.isActionDisabled('edit_${field.name}');
      
      return field.copyWith(enabled: !isReadOnly);
    }).toList();

    return builder(allowedFields);
  }
}

class FormFieldConfig {
  final String name;
  final String label;
  final bool enabled;
  final bool required;

  const FormFieldConfig({
    required this.name,
    required this.label,
    this.enabled = true,
    this.required = false,
  });

  FormFieldConfig copyWith({
    String? name,
    String? label,
    bool? enabled,
    bool? required,
  }) {
    return FormFieldConfig(
      name: name ?? this.name,
      label: label ?? this.label,
      enabled: enabled ?? this.enabled,
      required: required ?? this.required,
    );
  }
}

// Helper function to get current path permission
PathPermissionResult? getCurrentPathPermission(BuildContext context) {
  return _BreadcrumbPermissionProvider.of(context);
}

// Helper function to check component permission
bool hasComponentPermission(BuildContext context, String componentId, String permission) {
  final pathPermission = _BreadcrumbPermissionProvider.of(context);
  final componentPermission = pathPermission?.components[componentId];
  return componentPermission?.hasPermission(permission) ?? false;
}
