// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alert.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Alert _$AlertFromJson(Map<String, dynamic> json) => Alert(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String?,
      title: json['title'] as String,
      message: json['message'] as String,
      severity: json['severity'] as String,
      status: json['status'] as String,
      category: json['category'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      resolvedAt: json['resolvedAt'] as String?,
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AlertToJson(Alert instance) => <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'title': instance.title,
      'message': instance.message,
      'severity': instance.severity,
      'status': instance.status,
      'category': instance.category,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'resolvedAt': instance.resolvedAt,
      'property': instance.property,
    };

PropertyInfo _$PropertyInfoFromJson(Map<String, dynamic> json) => PropertyInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$PropertyInfoToJson(PropertyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };

CreateAlertRequest _$CreateAlertRequestFromJson(Map<String, dynamic> json) =>
    CreateAlertRequest(
      propertyId: json['propertyId'] as String?,
      title: json['title'] as String,
      message: json['message'] as String,
      severity: json['severity'] as String,
      category: json['category'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CreateAlertRequestToJson(CreateAlertRequest instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'title': instance.title,
      'message': instance.message,
      'severity': instance.severity,
      'category': instance.category,
      'metadata': instance.metadata,
    };

UpdateAlertRequest _$UpdateAlertRequestFromJson(Map<String, dynamic> json) =>
    UpdateAlertRequest(
      status: json['status'] as String?,
      resolvedAt: json['resolvedAt'] as String?,
    );

Map<String, dynamic> _$UpdateAlertRequestToJson(UpdateAlertRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'resolvedAt': instance.resolvedAt,
    };

AlertQueryParams _$AlertQueryParamsFromJson(Map<String, dynamic> json) =>
    AlertQueryParams(
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      severity: json['severity'] as String?,
      status: json['status'] as String?,
      propertyId: json['propertyId'] as String?,
    );

Map<String, dynamic> _$AlertQueryParamsToJson(AlertQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'severity': instance.severity,
      'status': instance.status,
      'propertyId': instance.propertyId,
    };

AlertStatistics _$AlertStatisticsFromJson(Map<String, dynamic> json) =>
    AlertStatistics(
      total: (json['total'] as num).toInt(),
      open: (json['open'] as num).toInt(),
      acknowledged: (json['acknowledged'] as num).toInt(),
      resolved: (json['resolved'] as num).toInt(),
      low: (json['low'] as num).toInt(),
      medium: (json['medium'] as num).toInt(),
      high: (json['high'] as num).toInt(),
      critical: (json['critical'] as num).toInt(),
      averageResolutionTime: (json['averageResolutionTime'] as num).toDouble(),
    );

Map<String, dynamic> _$AlertStatisticsToJson(AlertStatistics instance) =>
    <String, dynamic>{
      'total': instance.total,
      'open': instance.open,
      'acknowledged': instance.acknowledged,
      'resolved': instance.resolved,
      'low': instance.low,
      'medium': instance.medium,
      'high': instance.high,
      'critical': instance.critical,
      'averageResolutionTime': instance.averageResolutionTime,
    };
