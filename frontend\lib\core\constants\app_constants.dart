class AppConstants {
  // App Information
  static const String appName = 'SRSR Property Management';
  static const String appVersion = '1.0.0';
  
  // Database
  static const String databaseName = 'srsr_property_management.db';
  static const int databaseVersion = 1;
  
  // API Configuration
  static const String baseUrl = 'http://192.168.1.198:3000/';
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userRoleKey = 'user_role';
  static const String selectedPropertyKey = 'selected_property';
  static const String themeKey = 'theme_mode';
  
  // Property Types
  static const String residentialProperty = 'residential';
  static const String officeProperty = 'office';
  static const String constructionSite = 'construction';
  
  // System Categories
  static const String waterSystem = 'water';
  static const String electricitySystem = 'electricity';
  static const String securitySystem = 'security';
  static const String internetSystem = 'internet';
  static const String ottServices = 'ott';
  static const String maintenance = 'maintenance';
  
  // User Roles
  static const String superAdminRole = 'super_admin';
  static const String propertyManagerRole = 'property_manager';
  static const String officeManagerRole = 'office_manager';
  static const String securityPersonnelRole = 'security_personnel';
  static const String maintenanceStaffRole = 'maintenance_staff';
  static const String constructionSupervisorRole = 'construction_supervisor';
  
  // Status Types
  static const String operationalStatus = 'operational';
  static const String warningStatus = 'warning';
  static const String criticalStatus = 'critical';
  static const String offlineStatus = 'offline';
  
  // Issue Priority
  static const String lowPriority = 'low';
  static const String mediumPriority = 'medium';
  static const String highPriority = 'high';
  static const String criticalPriority = 'critical';
  
  // Issue Status
  static const String openIssue = 'open';
  static const String inProgressIssue = 'in_progress';
  static const String resolvedIssue = 'resolved';
  static const String closedIssue = 'closed';
  
  // Issue Types
  static const String oneTimeIssue = 'one_time';
  static const String recurrentIssue = 'recurrent';
  
  // Attendance Status
  static const String presentStatus = 'present';
  static const String absentStatus = 'absent';
  static const String lateStatus = 'late';
  static const String halfDayStatus = 'half_day';
  
  // Employee Types
  static const String fullTimeEmployee = 'full_time';
  static const String contractEmployee = 'contract';
  static const String supportStaff = 'support_staff';
  
  // Shift Types
  static const String dayShift = 'day';
  static const String nightShift = 'night';
  static const String morningShift = 'morning';
  static const String eveningShift = 'evening';
  
  // Report Types
  static const String dailyReport = 'daily';
  static const String weeklyReport = 'weekly';
  static const String monthlyReport = 'monthly';
  static const String annualReport = 'annual';
  
  // Notification Types
  static const String systemAlert = 'system_alert';
  static const String maintenanceReminder = 'maintenance_reminder';
  static const String securityAlert = 'security_alert';
  static const String attendanceNotification = 'attendance_notification';
  
  // Default Values
  static const int defaultPageSize = 20;
  static const int maxRetryAttempts = 3;
  static const Duration cacheExpiration = Duration(hours: 24);
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Grid Constants
  static const int dashboardGridColumns = 2;
  static const double gridSpacing = 16.0;
  static const double cardAspectRatio = 1.2;
  
  // Chart Colors
  static const List<String> chartColors = [
    '#4CAF50', // Green - Operational
    '#FF9800', // Orange - Warning
    '#F44336', // Red - Critical
    '#9E9E9E', // Grey - Offline
  ];
}
