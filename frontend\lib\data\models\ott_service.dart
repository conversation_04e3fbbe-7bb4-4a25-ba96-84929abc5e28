import 'package:json_annotation/json_annotation.dart';

part 'ott_service.g.dart';

@JsonSerializable()
class OTTService {
  final String id;
  final String propertyId;
  final String platform;
  final String plan;
  final String? loginId;
  final String? password;
  final String? nextPayment;
  final String status;
  final bool isActive;
  final String createdAt;
  final String updatedAt;

  const OTTService({
    required this.id,
    required this.propertyId,
    required this.platform,
    required this.plan,
    this.loginId,
    this.password,
    this.nextPayment,
    required this.status,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OTTService.fromJson(Map<String, dynamic> json) => _$OTTServiceFromJson(json);
  Map<String, dynamic> toJson() => _$OTTServiceToJson(this);

  OTTService copyWith({
    String? id,
    String? propertyId,
    String? platform,
    String? plan,
    String? loginId,
    String? password,
    String? nextPayment,
    String? status,
    bool? isActive,
    String? createdAt,
    String? updatedAt,
  }) {
    return OTTService(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      platform: platform ?? this.platform,
      plan: plan ?? this.plan,
      loginId: loginId ?? this.loginId,
      password: password ?? this.password,
      nextPayment: nextPayment ?? this.nextPayment,
      status: status ?? this.status,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isPending => status == 'PENDING';
  bool get isActiveStatus => status == 'ACTIVE';
  bool get isExpired => status == 'EXPIRED';
  bool get isCancelled => status == 'CANCELLED';

  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
  DateTime? get nextPaymentDate => nextPayment != null ? DateTime.parse(nextPayment!) : null;

  // Get status color for UI
  String get statusColor {
    switch (status) {
      case 'ACTIVE':
        return 'green';
      case 'PENDING':
        return 'orange';
      case 'EXPIRED':
        return 'red';
      case 'CANCELLED':
        return 'grey';
      default:
        return 'grey';
    }
  }

  // Get display status
  String get displayStatus {
    switch (status) {
      case 'ACTIVE':
        return 'Active';
      case 'PENDING':
        return 'Pending';
      case 'EXPIRED':
        return 'Expired';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status;
    }
  }

  // Check if payment is due soon (within 30 days)
  bool get isPaymentDueSoon {
    if (nextPaymentDate == null) return false;
    final now = DateTime.now();
    final daysUntilPayment = nextPaymentDate!.difference(now).inDays;
    return daysUntilPayment <= 30 && daysUntilPayment >= 0;
  }

  // Check if payment is overdue
  bool get isPaymentOverdue {
    if (nextPaymentDate == null) return false;
    return nextPaymentDate!.isBefore(DateTime.now());
  }
}

@JsonSerializable()
class CreateOTTServiceRequest {
  final String platform;
  final String plan;
  final String? loginId;
  final String? password;
  final String? nextPayment;
  final String? status;

  const CreateOTTServiceRequest({
    required this.platform,
    required this.plan,
    this.loginId,
    this.password,
    this.nextPayment,
    this.status,
  });

  factory CreateOTTServiceRequest.fromJson(Map<String, dynamic> json) => _$CreateOTTServiceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateOTTServiceRequestToJson(this);
}

@JsonSerializable()
class UpdateOTTServiceRequest {
  final String? platform;
  final String? plan;
  final String? loginId;
  final String? password;
  final String? nextPayment;
  final String? status;

  const UpdateOTTServiceRequest({
    this.platform,
    this.plan,
    this.loginId,
    this.password,
    this.nextPayment,
    this.status,
  });

  factory UpdateOTTServiceRequest.fromJson(Map<String, dynamic> json) => _$UpdateOTTServiceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateOTTServiceRequestToJson(this);
}

@JsonSerializable()
class OTTServiceResponse {
  final bool success;
  final OTTService? data;
  final String? message;
  final String? error;
  final String timestamp;

  const OTTServiceResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    required this.timestamp,
  });

  factory OTTServiceResponse.fromJson(Map<String, dynamic> json) => _$OTTServiceResponseFromJson(json);
  Map<String, dynamic> toJson() => _$OTTServiceResponseToJson(this);
}

@JsonSerializable()
class OTTServicesListResponse {
  final bool success;
  final List<OTTService>? data;
  final PaginationInfo? pagination;
  final String? message;
  final String? error;
  final String timestamp;

  const OTTServicesListResponse({
    required this.success,
    this.data,
    this.pagination,
    this.message,
    this.error,
    required this.timestamp,
  });

  factory OTTServicesListResponse.fromJson(Map<String, dynamic> json) => _$OTTServicesListResponseFromJson(json);
  Map<String, dynamic> toJson() => _$OTTServicesListResponseToJson(this);
}

@JsonSerializable()
class PaginationInfo {
  final int page;
  final int limit;
  final int total;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  const PaginationInfo({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) => _$PaginationInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PaginationInfoToJson(this);
}
