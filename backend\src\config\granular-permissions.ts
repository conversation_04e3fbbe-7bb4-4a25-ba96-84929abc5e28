// Granular Permission Definitions for Each Screen and Role

export interface TabPermission {
  tabId: string;
  tabName: string;
  accessLevel: 'full' | 'read_only' | 'restricted' | 'none';
  restrictions?: {
    hideFields?: string[];
    disableActions?: string[];
    limitData?: boolean;
    customConditions?: Record<string, any>;
  };
}

export interface ScreenPermissions {
  screen: string;
  tabs: Record<string, TabPermission>;
  globalRestrictions?: Record<string, any>;
}

// Security Management Screen Permissions
export const SECURITY_PERMISSIONS: Record<string, ScreenPermissions> = {
  SUPER_ADMIN: {
    screen: 'security_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'full'
      },
      cctv: {
        tabId: 'cctv',
        tabName: 'CCTV',
        accessLevel: 'full'
      },
      access_control: {
        tabId: 'access_control',
        tabName: 'Access Control',
        accessLevel: 'full'
      },
      maintenance: {
        tabId: 'maintenance',
        tabName: 'Maintenance',
        accessLevel: 'full'
      },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'full'
      }
    }
  },
  SECURITY_PERSONNEL: {
    screen: 'security_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'read_only',
        restrictions: {
          hideFields: ['installation_cost', 'vendor_contracts'],
          disableActions: ['edit_system_config']
        }
      },
      cctv: {
        tabId: 'cctv',
        tabName: 'CCTV',
        accessLevel: 'restricted',
        restrictions: {
          hideFields: ['camera_cost', 'vendor_details'],
          disableActions: ['add_camera', 'remove_camera', 'edit_camera_config'],
          customConditions: {
            canViewLiveFeeds: true,
            canControlPTZ: false,
            canAccessRecordings: true,
            recordingRetentionDays: 7 // Limited access to recent recordings
          }
        }
      },
      access_control: {
        tabId: 'access_control',
        tabName: 'Access Control',
        accessLevel: 'restricted',
        restrictions: {
          hideFields: ['system_cost', 'installation_details'],
          disableActions: ['change_master_code', 'add_remove_users'],
          customConditions: {
            canViewAccessLogs: true,
            canResetUserCodes: false,
            canEmergencyOverride: true
          }
        }
      },
      maintenance: {
        tabId: 'maintenance',
        tabName: 'Maintenance',
        accessLevel: 'read_only',
        restrictions: {
          hideFields: ['maintenance_cost', 'vendor_contacts'],
          disableActions: ['schedule_maintenance', 'approve_work_orders']
        }
      },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'restricted',
        restrictions: {
          hideFields: ['contract_details', 'payment_info'],
          customConditions: {
            emergencyContactsOnly: true
          }
        }
      }
    }
  },
  PROPERTY_MANAGER: {
    screen: 'security_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'full'
      },
      cctv: {
        tabId: 'cctv',
        tabName: 'CCTV',
        accessLevel: 'full'
      },
      access_control: {
        tabId: 'access_control',
        tabName: 'Access Control',
        accessLevel: 'full'
      },
      maintenance: {
        tabId: 'maintenance',
        tabName: 'Maintenance',
        accessLevel: 'full'
      },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'full'
      }
    }
  },
  MAINTENANCE_STAFF: {
    screen: 'security_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'read_only',
        restrictions: {
          hideFields: ['financial_data', 'strategic_info']
        }
      },
      cctv: {
        tabId: 'cctv',
        tabName: 'CCTV',
        accessLevel: 'restricted',
        restrictions: {
          hideFields: ['live_feeds', 'recordings'],
          customConditions: {
            maintenanceTasksOnly: true,
            canViewSystemHealth: true,
            canReportIssues: true
          }
        }
      },
      access_control: {
        tabId: 'access_control',
        tabName: 'Access Control',
        accessLevel: 'restricted',
        restrictions: {
          hideFields: ['access_logs', 'user_codes'],
          customConditions: {
            maintenanceTasksOnly: true,
            canTestSystems: true
          }
        }
      },
      maintenance: {
        tabId: 'maintenance',
        tabName: 'Maintenance',
        accessLevel: 'full'
      },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: {
            vendorContactsOnly: true
          }
        }
      }
    }
  },
  OFFICE_MANAGER: {
    screen: 'security_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'none'
      },
      cctv: {
        tabId: 'cctv',
        tabName: 'CCTV',
        accessLevel: 'none'
      },
      access_control: {
        tabId: 'access_control',
        tabName: 'Access Control',
        accessLevel: 'none'
      },
      maintenance: {
        tabId: 'maintenance',
        tabName: 'Maintenance',
        accessLevel: 'none'
      },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'none'
      }
    }
  }
};

// Electricity Management Screen Permissions
export const ELECTRICITY_PERMISSIONS: Record<string, ScreenPermissions> = {
  SUPER_ADMIN: {
    screen: 'electricity_management',
    tabs: {
      overview: { tabId: 'overview', tabName: 'Overview', accessLevel: 'full' },
      generator: { tabId: 'generator', tabName: 'Generator', accessLevel: 'full' },
      ups: { tabId: 'ups', tabName: 'UPS', accessLevel: 'full' },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'full' },
      contacts: { tabId: 'contacts', tabName: 'Contacts', accessLevel: 'full' }
    }
  },
  PROPERTY_MANAGER: {
    screen: 'electricity_management',
    tabs: {
      overview: { tabId: 'overview', tabName: 'Overview', accessLevel: 'full' },
      generator: { tabId: 'generator', tabName: 'Generator', accessLevel: 'full' },
      ups: { tabId: 'ups', tabName: 'UPS', accessLevel: 'full' },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'full' },
      contacts: { tabId: 'contacts', tabName: 'Contacts', accessLevel: 'full' }
    }
  },
  MAINTENANCE_STAFF: {
    screen: 'electricity_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'read_only',
        restrictions: {
          hideFields: ['cost_analysis', 'vendor_contracts']
        }
      },
      generator: {
        tabId: 'generator',
        tabName: 'Generator',
        accessLevel: 'restricted',
        restrictions: {
          hideFields: ['purchase_cost', 'warranty_details'],
          customConditions: {
            canUpdateFuelLevel: true,
            canRunTests: true,
            canViewMaintenanceSchedule: true
          }
        }
      },
      ups: {
        tabId: 'ups',
        tabName: 'UPS',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: {
            canCheckBatteryStatus: true,
            canRunDiagnostics: true,
            canReplaceBatteries: true
          }
        }
      },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'full' },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: { vendorContactsOnly: true }
        }
      }
    }
  },
  SECURITY_PERSONNEL: {
    screen: 'electricity_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'read_only',
        restrictions: {
          hideFields: ['detailed_specs', 'cost_info'],
          customConditions: { emergencyInfoOnly: true }
        }
      },
      generator: {
        tabId: 'generator',
        tabName: 'Generator',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: {
            canViewFuelLevel: true,
            canStartGenerator: true, // Emergency situations
            emergencyUseOnly: true
          }
        }
      },
      ups: { tabId: 'ups', tabName: 'UPS', accessLevel: 'read_only' },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'none' },
      contacts: {
        tabId: 'contacts',
        tabName: 'Contacts',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: { emergencyContactsOnly: true }
        }
      }
    }
  }
};

// Water Management Screen Permissions
export const WATER_PERMISSIONS: Record<string, ScreenPermissions> = {
  SUPER_ADMIN: {
    screen: 'water_management',
    tabs: {
      overview: { tabId: 'overview', tabName: 'Overview', accessLevel: 'full' },
      tank_monitoring: { tabId: 'tank_monitoring', tabName: 'Tank Monitoring', accessLevel: 'full' },
      pump_control: { tabId: 'pump_control', tabName: 'Pump Control', accessLevel: 'full' },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'full' },
      quality_reports: { tabId: 'quality_reports', tabName: 'Quality Reports', accessLevel: 'full' }
    }
  },
  PROPERTY_MANAGER: {
    screen: 'water_management',
    tabs: {
      overview: { tabId: 'overview', tabName: 'Overview', accessLevel: 'full' },
      tank_monitoring: { tabId: 'tank_monitoring', tabName: 'Tank Monitoring', accessLevel: 'full' },
      pump_control: { tabId: 'pump_control', tabName: 'Pump Control', accessLevel: 'full' },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'full' },
      quality_reports: { tabId: 'quality_reports', tabName: 'Quality Reports', accessLevel: 'full' }
    }
  },
  MAINTENANCE_STAFF: {
    screen: 'water_management',
    tabs: {
      overview: {
        tabId: 'overview',
        tabName: 'Overview',
        accessLevel: 'read_only',
        restrictions: { hideFields: ['cost_analysis'] }
      },
      tank_monitoring: {
        tabId: 'tank_monitoring',
        tabName: 'Tank Monitoring',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: {
            canViewLevels: true,
            canCleanTanks: true,
            canReportIssues: true
          }
        }
      },
      pump_control: {
        tabId: 'pump_control',
        tabName: 'Pump Control',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: {
            canOperatePumps: true,
            canPerformMaintenance: true,
            emergencyControlOnly: false
          }
        }
      },
      maintenance: { tabId: 'maintenance', tabName: 'Maintenance', accessLevel: 'full' },
      quality_reports: {
        tabId: 'quality_reports',
        tabName: 'Quality Reports',
        accessLevel: 'restricted',
        restrictions: {
          customConditions: { canSubmitReports: true, canViewHistory: false }
        }
      }
    }
  }
};
