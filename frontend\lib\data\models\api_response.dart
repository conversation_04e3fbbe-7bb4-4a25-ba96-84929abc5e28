import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final String timestamp;
  final String? path;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    required this.timestamp,
    this.path,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  // Success factory
  factory ApiResponse.success({
    required T data,
    String? message,
    String? path,
  }) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
      timestamp: DateTime.now().toIso8601String(),
      path: path,
    );
  }

  // Error factory
  factory ApiResponse.error({
    required String error,
    String? message,
    String? path,
  }) {
    return ApiResponse(
      success: false,
      error: error,
      message: message,
      timestamp: DateTime.now().toIso8601String(),
      path: path,
    );
  }

  bool get isSuccess => success && error == null;
  bool get isError => !success || error != null;
}

@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> {
  final List<T> data;
  final Pagination pagination;

  const PaginatedResponse({
    required this.data,
    required this.pagination,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);
}

@JsonSerializable()
class Pagination {
  final int page;
  final int limit;
  final int total;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  const Pagination({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) =>
      _$PaginationFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationToJson(this);
}

@JsonSerializable()
class ErrorResponse {
  final String error;
  final String message;
  final String timestamp;
  final String? path;
  final Map<String, dynamic>? details;
  final int? retryAfter;

  const ErrorResponse({
    required this.error,
    required this.message,
    required this.timestamp,
    this.path,
    this.details,
    this.retryAfter,
  });

  factory ErrorResponse.fromJson(Map<String, dynamic> json) =>
      _$ErrorResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorResponseToJson(this);
}

@JsonSerializable()
class HealthResponse {
  final String status;
  final String timestamp;
  final double uptime;
  final String version;

  const HealthResponse({
    required this.status,
    required this.timestamp,
    required this.uptime,
    required this.version,
  });

  factory HealthResponse.fromJson(Map<String, dynamic> json) =>
      _$HealthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$HealthResponseToJson(this);

  bool get isHealthy => status == 'healthy';
}

@JsonSerializable()
class DetailedHealthResponse extends HealthResponse {
  final Map<String, ServiceHealth> services;
  final HealthMetrics metrics;

  const DetailedHealthResponse({
    required super.status,
    required super.timestamp,
    required super.uptime,
    required super.version,
    required this.services,
    required this.metrics,
  });

  factory DetailedHealthResponse.fromJson(Map<String, dynamic> json) =>
      _$DetailedHealthResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$DetailedHealthResponseToJson(this);
}

@JsonSerializable()
class ServiceHealth {
  final String status;
  final double responseTime;
  final String lastCheck;
  final String? error;

  const ServiceHealth({
    required this.status,
    required this.responseTime,
    required this.lastCheck,
    this.error,
  });

  factory ServiceHealth.fromJson(Map<String, dynamic> json) =>
      _$ServiceHealthFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceHealthToJson(this);

  bool get isHealthy => status == 'healthy';
}

@JsonSerializable()
class HealthMetrics {
  final double memoryUsage;
  final double cpuUsage;
  final int activeConnections;

  const HealthMetrics({
    required this.memoryUsage,
    required this.cpuUsage,
    required this.activeConnections,
  });

  factory HealthMetrics.fromJson(Map<String, dynamic> json) =>
      _$HealthMetricsFromJson(json);

  Map<String, dynamic> toJson() => _$HealthMetricsToJson(this);
}

@JsonSerializable()
class FileUploadResponse {
  final String filename;
  final String originalName;
  final String mimetype;
  final int size;
  final String url;
  final String uploadedAt;

  const FileUploadResponse({
    required this.filename,
    required this.originalName,
    required this.mimetype,
    required this.size,
    required this.url,
    required this.uploadedAt,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$FileUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FileUploadResponseToJson(this);
}

// Generic list response for simple arrays
@JsonSerializable(genericArgumentFactories: true)
class ListResponse<T> {
  final List<T> data;

  const ListResponse({
    required this.data,
  });

  factory ListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ListResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ListResponseToJson(this, toJsonT);
}


