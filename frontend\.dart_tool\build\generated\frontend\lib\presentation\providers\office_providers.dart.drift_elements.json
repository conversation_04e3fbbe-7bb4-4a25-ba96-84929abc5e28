{"valid_import": true, "imports": [{"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/data/models/office.dart", "transitive": false}, {"uri": "package:frontend/data/models/attendance.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/office_repository.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}], "elements": []}