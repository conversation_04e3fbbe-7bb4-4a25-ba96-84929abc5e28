// Enhanced RBAC/UBAC Schema Extension
// Add these models to the existing schema.prisma

// Enhanced Permission Management with Granular Control
model Permission {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  category    String   // 'screen', 'tab', 'section', 'widget', 'action', 'data'
  resource    String   // 'dashboard', 'properties', 'maintenance', etc.
  subResource String?  // 'overview', 'cctv', 'access_control', 'fuel_management', etc.
  action      String   // 'create', 'read', 'update', 'delete', 'view', 'export'
  level       String   // 'full', 'read_only', 'restricted', 'none'
  conditions  Json?    @default("{}") // ABAC conditions
  hierarchy   Int      @default(0) // Permission hierarchy level
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  rolePermissions RolePermission[]
  userPermissions UserPermissionOverride[]

  @@map("permissions")
}

model RolePermission {
  id           String     @id @default(uuid())
  roleId       String
  permissionId String
  isGranted    Boolean    @default(true)
  conditions   Json?      @default("{}") // Additional role-specific conditions
  createdAt    DateTime   @default(now())

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model Role {
  id          String   @id @default(uuid())
  name        String   @unique
  displayName String
  description String?
  level       Int      @default(0) // Hierarchy level for role inheritance
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  users           User[]
  rolePermissions RolePermission[]
  parentRoles     RoleHierarchy[] @relation("ParentRole")
  childRoles      RoleHierarchy[] @relation("ChildRole")

  @@map("roles")
}

model RoleHierarchy {
  id         String @id @default(uuid())
  parentId   String
  childId    String
  createdAt  DateTime @default(now())

  parent Role @relation("ParentRole", fields: [parentId], references: [id], onDelete: Cascade)
  child  Role @relation("ChildRole", fields: [childId], references: [id], onDelete: Cascade)

  @@unique([parentId, childId])
  @@map("role_hierarchy")
}

// User-specific permission overrides
model UserPermissionOverride {
  id           String     @id @default(uuid())
  userId       String
  permissionId String
  isGranted    Boolean    // true = grant, false = deny
  reason       String?    // Reason for override
  expiresAt    DateTime?  // Optional expiration
  createdBy    String
  createdAt    DateTime   @default(now())

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  creator    User       @relation("CreatedOverrides", fields: [createdBy], references: [id])

  @@unique([userId, permissionId])
  @@map("user_permission_overrides")
}

// Attribute-Based Access Control (ABAC)
model AccessPolicy {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  resource    String   // Resource type (property, office, system, etc.)
  action      String   // Action type (view, edit, delete, etc.)
  conditions  Json     // ABAC conditions in JSON format
  effect      String   // 'ALLOW' or 'DENY'
  priority    Int      @default(0) // Higher priority = evaluated first
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("access_policies")
}

// Permission audit trail
model PermissionAudit {
  id           String   @id @default(uuid())
  userId       String
  resource     String
  action       String
  permission   String
  granted      Boolean
  reason       String?
  context      Json?    @default("{}")
  ipAddress    String?
  userAgent    String?
  createdAt    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("permission_audits")
}

// Enhanced UI Component Permissions with Tab/Section Control
model UIComponentPermission {
  id          String   @id @default(uuid())
  componentId String   // Unique identifier for UI component
  screen      String   // Screen name (e.g., 'security_management')
  tab         String?  // Tab name (e.g., 'cctv', 'access_control', 'overview')
  section     String?  // Section name (e.g., 'camera_list', 'maintenance_schedule')
  widget      String   // Widget/component name
  roleId      String
  accessLevel String   @default("full") // 'full', 'read_only', 'restricted', 'none'
  isVisible   Boolean  @default(true)
  isEnabled   Boolean  @default(true)
  conditions  Json?    @default("{}") // Display conditions
  restrictions Json?   @default("{}") // Feature restrictions within component
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([componentId, roleId])
  @@map("ui_component_permissions")
}

// Tab-level permissions for granular screen control
model TabPermission {
  id          String   @id @default(uuid())
  screen      String   // Screen name
  tabId       String   // Tab identifier
  tabName     String   // Display name of tab
  roleId      String
  accessLevel String   @default("full") // 'full', 'read_only', 'restricted', 'none'
  isVisible   Boolean  @default(true)
  isEnabled   Boolean  @default(true)
  restrictions Json?   @default("{}") // Tab-specific restrictions
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([screen, tabId, roleId])
  @@map("tab_permissions")
}

// Data filtering rules
model DataFilter {
  id         String   @id @default(uuid())
  roleId     String
  resource   String   // Table/model name
  filterType String   // 'WHERE', 'SELECT', 'JOIN'
  conditions Json     // Filter conditions
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@map("data_filters")
}

// Update existing User model to reference new Role model
// Add these fields to existing User model:
// roleId      String?
// role        Role?    @relation(fields: [roleId], references: [id])
// permissionOverrides UserPermissionOverride[]
// createdOverrides    UserPermissionOverride[] @relation("CreatedOverrides")
// permissionAudits    PermissionAudit[]
