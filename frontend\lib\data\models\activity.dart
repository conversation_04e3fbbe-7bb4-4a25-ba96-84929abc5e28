import 'package:json_annotation/json_annotation.dart';

part 'activity.g.dart';

@JsonSerializable()
class Activity {
  final String id;
  final String? userId;
  final String? propertyId;
  final String action;
  final String description;
  final Map<String, dynamic>? metadata;
  final String? ipAddress;
  final String? userAgent;
  final String createdAt;
  final UserInfo? user;
  final PropertyInfo? property;

  const Activity({
    required this.id,
    this.userId,
    this.propertyId,
    required this.action,
    required this.description,
    this.metadata,
    this.ipAddress,
    this.userAgent,
    required this.createdAt,
    this.user,
    this.property,
  });

  factory Activity.fromJson(Map<String, dynamic> json) => _$ActivityFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityToJson(this);

  Activity copyWith({
    String? id,
    String? userId,
    String? propertyId,
    String? action,
    String? description,
    Map<String, dynamic>? metadata,
    String? ipAddress,
    String? userAgent,
    String? createdAt,
    UserInfo? user,
    PropertyInfo? property,
  }) {
    return Activity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      propertyId: propertyId ?? this.propertyId,
      action: action ?? this.action,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      createdAt: createdAt ?? this.createdAt,
      user: user ?? this.user,
      property: property ?? this.property,
    );
  }

  // Helper getters
  DateTime get createdDateTime => DateTime.parse(createdAt);
  Duration get age => DateTime.now().difference(createdDateTime);
  
  bool get isSystemAction => action.startsWith('SYSTEM_');
  bool get isUserAction => action.startsWith('USER_');
  bool get isPropertyAction => action.contains('PROPERTY');
  bool get isMaintenanceAction => action.contains('MAINTENANCE');
  bool get isSecurityAction => action.contains('SECURITY');
  bool get isAttendanceAction => action.contains('ATTENDANCE');
  
  String get actionCategory {
    if (isSystemAction) return 'System';
    if (isPropertyAction) return 'Property';
    if (isMaintenanceAction) return 'Maintenance';
    if (isSecurityAction) return 'Security';
    if (isAttendanceAction) return 'Attendance';
    return 'General';
  }
}

@JsonSerializable()
class UserInfo {
  final String id;
  final String name;
  final String email;

  const UserInfo({
    required this.id,
    required this.name,
    required this.email,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);
  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;

  const PropertyInfo({
    required this.id,
    required this.name,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

@JsonSerializable()
class ActivityQueryParams {
  final int? page;
  final int? limit;
  final String? userId;
  final String? propertyId;
  final String? action;
  final String? startDate;
  final String? endDate;

  const ActivityQueryParams({
    this.page,
    this.limit,
    this.userId,
    this.propertyId,
    this.action,
    this.startDate,
    this.endDate,
  });

  factory ActivityQueryParams.fromJson(Map<String, dynamic> json) => _$ActivityQueryParamsFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityQueryParamsToJson(this);

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{};
    if (page != null) params['page'] = page.toString();
    if (limit != null) params['limit'] = limit.toString();
    if (userId != null) params['userId'] = userId;
    if (propertyId != null) params['propertyId'] = propertyId;
    if (action != null) params['action'] = action;
    if (startDate != null) params['startDate'] = startDate;
    if (endDate != null) params['endDate'] = endDate;
    return params;
  }
}

// Activity statistics
@JsonSerializable()
class ActivityStatistics {
  final int total;
  final int today;
  final int thisWeek;
  final int thisMonth;
  final Map<String, int> byAction;
  final Map<String, int> byUser;
  final Map<String, int> byProperty;

  const ActivityStatistics({
    required this.total,
    required this.today,
    required this.thisWeek,
    required this.thisMonth,
    required this.byAction,
    required this.byUser,
    required this.byProperty,
  });

  factory ActivityStatistics.fromJson(Map<String, dynamic> json) => _$ActivityStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityStatisticsToJson(this);

  // Helper getters
  List<MapEntry<String, int>> get topActions => 
      byAction.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
  
  List<MapEntry<String, int>> get topUsers => 
      byUser.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
  
  List<MapEntry<String, int>> get topProperties => 
      byProperty.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
}
