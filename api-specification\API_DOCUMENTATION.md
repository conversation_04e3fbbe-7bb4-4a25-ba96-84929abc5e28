# SRSR Property Management API Documentation

## Overview

This comprehensive API specification supports the SRSR Property Management Flutter mobile application with full RBAC (Role-Based Access Control), real-time updates, and multi-property management capabilities.

## 🔐 Authentication & Authorization

### JWT Token-Based Authentication
- **Access Token**: Short-lived token (15 minutes) for API access
- **Refresh Token**: Long-lived token (7 days) for token renewal
- **Bearer Token**: Include in Authorization header: `Bearer {access_token}`

### Role-Based Access Control (RBAC)

#### User Roles
1. **Super Admin** (`super_admin`)
   - Full system access
   - User management
   - All properties and offices
   - System configuration

2. **Property Manager** (`property_manager`)
   - Assigned properties management
   - System monitoring and maintenance
   - Reports and analytics
   - No user management

3. **Office Manager** (`office_manager`)
   - Office and construction site management
   - Attendance tracking
   - Employee management
   - Reports for assigned locations

4. **Security Personnel** (`security_personnel`)
   - Security system monitoring
   - CCTV access
   - Guard logs and reports
   - Limited to security functions

5. **Maintenance Staff** (`maintenance_staff`)
   - Maintenance issue management
   - Work order tracking
   - System status updates
   - Limited to maintenance functions

6. **Construction Supervisor** (`construction_supervisor`)
   - Construction site management
   - Worker attendance
   - Progress tracking
   - Site-specific reports

### Permission Matrix

| Feature | Super Admin | Property Manager | Office Manager | Security | Maintenance | Construction |
|---------|-------------|------------------|----------------|----------|-------------|--------------|
| Dashboard | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Properties | ✅ | ✅ (assigned) | ❌ | ❌ | ❌ | ❌ |
| Office Management | ✅ | ❌ | ✅ | ❌ | ❌ | ✅ (sites) |
| User Management | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Security Systems | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ |
| Maintenance | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ |
| Reports | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ |
| Export Data | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ |

## 🏢 Property Management

### Property Types
- **Residential**: Jublee Hills Home, Gandipet Guest House
- **Office**: Brane Back Office, STRF Office, Road 36 Office
- **Construction**: Gandipet 1-4, Bachupally sites

### System Types
- **Water**: Municipal connections, borewells, tanks, automation
- **Electricity**: Grid power, generators, UPS, solar
- **Security**: CCTV, access control, guards, alarms
- **Internet**: Broadband, WiFi, backup connections
- **OTT**: Streaming services, entertainment systems
- **Maintenance**: Scheduled tasks, issues, work orders

### System Status Levels
- **Operational** (Green): System functioning normally
- **Warning** (Orange): Attention required, non-critical
- **Critical** (Red): Immediate action needed
- **Offline** (Grey): System not responding

## 📊 Dashboard Features

### Real-time Monitoring
- System health overview with percentage scores
- Property status summaries
- Critical alerts feed
- Recent activity logs

### Analytics
- System performance trends
- Attendance statistics
- Maintenance schedules
- Cost tracking

## 🏢 Office Management

### Office Types
- **Back Office - Brane**: Administrative operations
- **STRF Office - Brane**: Specialized functions
- **Road No. 36 office - SRSR**: Main office
- **Back Office - SRSR**: Secondary operations

### Construction Sites
- **Gandipet 1**: Residential development
- **Gandipet 2**: Commercial project
- **Gandipet 3 & 4**: Mixed-use development
- **Bachupally**: Infrastructure project

### Attendance Management
- Daily attendance tracking
- Multiple shift support
- Overtime calculations
- Leave management
- Performance metrics

## 🔄 Real-time Updates

### Push Notifications
- Critical system alerts
- Maintenance reminders
- Attendance notifications
- Security alerts

## 📱 Mobile App Integration

### Offline Support
- Local data caching
- Sync when online
- Conflict resolution
- Background updates

### Device Features
- Biometric authentication
- Camera for documentation
- GPS for location tracking
- Push notifications

## 🛡️ Security Features

### Data Protection
- JWT token encryption
- API rate limiting
- Input validation
- SQL injection prevention
- XSS protection

### Audit Logging
- User action tracking
- System change logs
- Access attempt logs
- Data modification history

## 📈 API Usage Examples

### Authentication Flow
```bash
# Login
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "admin123",
  "deviceId": "device-123"
}

# Response
{
  "user": { ... },
  "token": {
    "accessToken": "eyJ...",
    "refreshToken": "eyJ...",
    "expiresAt": "2024-12-31T23:59:59Z"
  }
}

# Use token in subsequent requests
Authorization: Bearer eyJ...
```

### Property Management
```bash
# Get properties (filtered by user permissions)
GET /properties?type=residential&status=operational

# Get property details
GET /properties/{propertyId}

# Update system status
PUT /properties/{propertyId}/systems/water
{
  "status": "warning",
  "description": "Tank level low",
  "metadata": {
    "level": 25,
    "threshold": 30
  }
}
```

### Dashboard Data
```bash
# Get dashboard overview
GET /dashboard/overview?timeRange=24h

# Get critical alerts
GET /dashboard/alerts?severity=critical&status=open
```

### Office Management
```bash
# Submit attendance
POST /offices/{officeId}/attendance
{
  "date": "2024-01-15",
  "records": [
    {
      "employeeId": "emp-123",
      "status": "present",
      "checkInTime": "09:15",
      "checkOutTime": "18:45",
      "hoursWorked": 9
    }
  ]
}
```

## 🔧 Error Handling

### Standard Error Response
```json
{
  "error": "VALIDATION_ERROR",
  "message": "Invalid input data",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/properties",
  "details": {
    "field": "name",
    "issue": "required"
  }
}
```

### HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **429**: Rate Limited
- **500**: Internal Server Error

## 📊 Rate Limiting

### Limits by Endpoint Type
- **Authentication**: 5 requests/minute
- **Read Operations**: 100 requests/minute
- **Write Operations**: 30 requests/minute
- **File Uploads**: 10 requests/minute

### Headers
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## 🚀 Performance Optimization

### Caching Strategy
- **Redis**: Session and frequently accessed data
- **CDN**: Static assets and images
- **Database**: Query result caching
- **Client**: Local storage for offline support

### Pagination
- Default: 20 items per page
- Maximum: 100 items per page
- Cursor-based for large datasets
- Total count included in response

## 🔍 Monitoring & Analytics

### Metrics Tracked
- API response times
- Error rates by endpoint
- User activity patterns
- System performance metrics
- Business KPIs

### Health Checks
- `/health` - Basic health check
- `/health/detailed` - Comprehensive system status
- `/metrics` - Prometheus metrics endpoint

## 📝 Development Guidelines

### API Versioning
- URL versioning: `/v1/`, `/v2/`
- Backward compatibility maintained
- Deprecation notices with timeline
- Migration guides provided

### Testing
- Unit tests for all endpoints
- Integration tests for workflows
- Load testing for performance
- Security testing for vulnerabilities

This API specification provides a robust foundation for the SRSR Property Management system with comprehensive RBAC, real-time capabilities, and scalable architecture.
