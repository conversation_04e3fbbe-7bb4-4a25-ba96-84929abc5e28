import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../data/models/dashboard.dart';

class SystemHealthChart extends StatelessWidget {
  final List<SystemStatusOverview> systemStatuses;
  final Map<String, dynamic> stats;

  const SystemHealthChart({
    super.key,
    required this.systemStatuses,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.pie_chart,
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'System Health',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Pie Chart
            SizedBox(
              height: 200,
              child: _buildPieChart(),
            ),
            
            const SizedBox(height: 20),
            
            // Legend
            _buildLegend(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    final operationalSystems = stats['operationalSystems'] as int;
    final warningSystems = stats['warningSystems'] as int;
    final criticalSystems = stats['criticalSystems'] as int;
    final offlineSystems = stats['offlineSystems'] as int;
    final totalSystems = stats['totalSystems'] as int;

    if (totalSystems == 0) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'No System Data',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    final sections = <PieChartSectionData>[];

    if (operationalSystems > 0) {
      sections.add(
        PieChartSectionData(
          value: operationalSystems.toDouble(),
          title: '$operationalSystems',
          color: AppTheme.successColor,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (warningSystems > 0) {
      sections.add(
        PieChartSectionData(
          value: warningSystems.toDouble(),
          title: '$warningSystems',
          color: AppTheme.warningColor,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (criticalSystems > 0) {
      sections.add(
        PieChartSectionData(
          value: criticalSystems.toDouble(),
          title: '$criticalSystems',
          color: AppTheme.errorColor,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (offlineSystems > 0) {
      sections.add(
        PieChartSectionData(
          value: offlineSystems.toDouble(),
          title: '$offlineSystems',
          color: Colors.grey,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 40,
        sectionsSpace: 2,
        startDegreeOffset: -90,
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    final operationalSystems = stats['operationalSystems'] as int;
    final warningSystems = stats['warningSystems'] as int;
    final criticalSystems = stats['criticalSystems'] as int;
    final offlineSystems = stats['offlineSystems'] as int;
    final totalSystems = stats['totalSystems'] as int;

    return Column(
      children: [
        if (operationalSystems > 0)
          _buildLegendItem(
            'Operational',
            AppTheme.successColor,
            operationalSystems,
            totalSystems,
          ),
        if (warningSystems > 0)
          _buildLegendItem(
            'Warning',
            AppTheme.warningColor,
            warningSystems,
            totalSystems,
          ),
        if (criticalSystems > 0)
          _buildLegendItem(
            'Critical',
            AppTheme.errorColor,
            criticalSystems,
            totalSystems,
          ),
        if (offlineSystems > 0)
          _buildLegendItem(
            'Offline',
            Colors.grey,
            offlineSystems,
            totalSystems,
          ),
        
        const SizedBox(height: 12),
        
        // Total
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Systems',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                totalSystems.toString(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color, int count, int total) {
    final percentage = total > 0 ? (count / total) * 100 : 0.0;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '$count (${percentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
