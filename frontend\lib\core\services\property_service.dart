import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';
import '../../data/models/property.dart';
import '../../data/models/dashboard.dart';
import 'api_client.dart';

class PropertyService {
  static final PropertyService _instance = PropertyService._internal();
  factory PropertyService() => _instance;
  PropertyService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get paginated list of properties
  Future<ApiResponse<PaginatedResponse<PropertySummary>>> getProperties({
    int page = 1,
    int limit = ApiConstants.defaultPageSize,
    String? type,
    String? status,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;
      if (search != null) queryParams['search'] = search;

      final response = await _apiClient.get<PaginatedResponse<PropertySummary>>(
        ApiConstants.propertiesList,
        queryParameters: queryParams,
        fromJson: (json) => PaginatedResponse.fromJson(
          json,
          (item) => PropertySummary.fromJson(item as Map<String, dynamic>),
        ),
      );

      return response;
    } catch (e) {
      return ApiResponse<PaginatedResponse<PropertySummary>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch properties: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get property details
  Future<ApiResponse<PropertyDetail>> getPropertyDetail(String propertyId) async {
    try {
      final path = ApiConstants.propertyDetail.replaceAll('{propertyId}', propertyId);
      
      final response = await _apiClient.get<PropertyDetail>(
        path,
        fromJson: (json) => PropertyDetail.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<PropertyDetail>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch property details: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Create new property
  Future<ApiResponse<Property>> createProperty({
    required String name,
    required String type,
    required String address,
    required String description,
    double? latitude,
    double? longitude,
    List<String>? images,
  }) async {
    try {
      final data = {
        'name': name,
        'type': type,
        'address': address,
        'description': description,
        if (latitude != null) 'latitude': latitude,
        if (longitude != null) 'longitude': longitude,
        if (images != null) 'images': images,
      };

      final response = await _apiClient.post<Property>(
        ApiConstants.propertiesList,
        data: data,
        fromJson: (json) => Property.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<Property>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to create property: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Update property
  Future<ApiResponse<Property>> updateProperty({
    required String propertyId,
    String? name,
    String? address,
    String? description,
    double? latitude,
    double? longitude,
    List<String>? images,
    bool? isActive,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (address != null) data['address'] = address;
      if (description != null) data['description'] = description;
      if (latitude != null) data['latitude'] = latitude;
      if (longitude != null) data['longitude'] = longitude;
      if (images != null) data['images'] = images;
      if (isActive != null) data['isActive'] = isActive;

      final path = ApiConstants.propertyDetail.replaceAll('{propertyId}', propertyId);

      final response = await _apiClient.put<Property>(
        path,
        data: data,
        fromJson: (json) => Property.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<Property>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to update property: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get property system statuses
  Future<ApiResponse<List<SystemStatusSummary>>> getPropertySystems({
    required String propertyId,
    String? systemType,
  }) async {
    try {
      final path = ApiConstants.propertySystems.replaceAll('{propertyId}', propertyId);
      final queryParams = <String, dynamic>{};
      if (systemType != null) queryParams['systemType'] = systemType;

      final response = await _apiClient.get<List<SystemStatusSummary>>(
        path,
        queryParameters: queryParams,
        fromJson: (json) => (json as List)
            .map((item) => SystemStatusSummary.fromJson(item as Map<String, dynamic>))
            .toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<SystemStatusSummary>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch system statuses: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get specific system status
  Future<ApiResponse<SystemStatus>> getSystemStatus({
    required String propertyId,
    required String systemType,
  }) async {
    try {
      final path = ApiConstants.propertySystemDetail
          .replaceAll('{propertyId}', propertyId)
          .replaceAll('{systemType}', systemType);

      final response = await _apiClient.get<SystemStatus>(
        path,
        fromJson: (json) => SystemStatus.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<SystemStatus>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch system status: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Update system status
  Future<ApiResponse<SystemStatus>> updateSystemStatus({
    required String propertyId,
    required String systemType,
    required String status,
    String? description,
    Map<String, dynamic>? metadata,
    double? healthScore,
  }) async {
    try {
      final data = {
        'status': status,
        if (description != null) 'description': description,
        if (metadata != null) 'metadata': metadata,
        if (healthScore != null) 'healthScore': healthScore,
      };

      final path = ApiConstants.propertySystemDetail
          .replaceAll('{propertyId}', propertyId)
          .replaceAll('{systemType}', systemType);

      final response = await _apiClient.put<SystemStatus>(
        path,
        data: data,
        fromJson: (json) => SystemStatus.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<SystemStatus>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to update system status: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get property types for dropdown
  List<String> getPropertyTypes() {
    return ApiConstants.propertyTypes;
  }

  // Get system types for dropdown
  List<String> getSystemTypes() {
    return ApiConstants.systemTypes;
  }

  // Get system statuses for dropdown
  List<String> getSystemStatuses() {
    return ApiConstants.systemStatuses;
  }

  // Helper method to get property type display name
  String getPropertyTypeDisplayName(String type) {
    switch (type) {
      case 'RESIDENTIAL':
        return 'Residential';
      case 'OFFICE':
        return 'Office';
      case 'CONSTRUCTION':
        return 'Construction';
      default:
        return type;
    }
  }

  // Helper method to get system type display name
  String getSystemTypeDisplayName(String type) {
    switch (type) {
      case 'WATER':
        return 'Water';
      case 'ELECTRICITY':
        return 'Electricity';
      case 'SECURITY':
        return 'Security';
      case 'INTERNET':
        return 'Internet';
      case 'OTT':
        return 'OTT';
      case 'MAINTENANCE':
        return 'Maintenance';
      default:
        return type;
    }
  }

  // Helper method to get system status display name and color
  Map<String, dynamic> getSystemStatusInfo(String status) {
    switch (status) {
      case 'OPERATIONAL':
        return {
          'name': 'Operational',
          'color': 0xFF4CAF50, // Green
          'icon': 'check_circle',
        };
      case 'WARNING':
        return {
          'name': 'Warning',
          'color': 0xFFFF9800, // Orange
          'icon': 'warning',
        };
      case 'CRITICAL':
        return {
          'name': 'Critical',
          'color': 0xFFF44336, // Red
          'icon': 'error',
        };
      case 'OFFLINE':
        return {
          'name': 'Offline',
          'color': 0xFF9E9E9E, // Grey
          'icon': 'offline_bolt',
        };
      default:
        return {
          'name': status,
          'color': 0xFF9E9E9E,
          'icon': 'help',
        };
    }
  }
}
