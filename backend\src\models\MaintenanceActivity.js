const mongoose = require('mongoose');

const maintenanceActivitySchema = new mongoose.Schema({
  issueId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MaintenanceIssue',
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'CREATED',
      'ASSIGNED',
      'STATUS_CHANGED',
      'PRIORITY_CHANGED',
      'RESOLVED',
      'CLOSED',
      'REOPENED',
      'COMMENT_ADDED',
      'ATTACHMENT_ADDED',
      'UPDATED'
    ]
  },
  oldValue: {
    type: String,
    default: null
  },
  newValue: {
    type: String,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for user info
maintenanceActivitySchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

module.exports = mongoose.model('MaintenanceActivity', maintenanceActivitySchema);
