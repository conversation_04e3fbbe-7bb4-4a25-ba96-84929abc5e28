import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/main.dart' as app;
import 'package:frontend/core/services/service_locator.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Dashboard V2 Integration Tests', () {
    testWidgets('Complete Dashboard V2 Flow', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should be on login screen if not authenticated
      expect(find.text('Login'), findsOneWidget);

      // Enter test credentials
      await tester.enterText(
        find.byType(TextFormField).first,
        '<EMAIL>',
      );
      await tester.enterText(
        find.byType(TextFormField).last,
        'admin123',
      );

      // Tap login button
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should now be on Dashboard V1
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('V2'), findsOneWidget);

      // Test V2 access via app bar button
      await tester.tap(find.text('V2'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should now be on Dashboard V2
      expect(find.text('Dashboard V2'), findsOneWidget);
      expect(find.text('V1'), findsOneWidget);

      // Test time range selector
      await tester.tap(find.byIcon(Icons.schedule));
      await tester.pumpAndSettle();
      expect(find.text('Last 7 Days'), findsOneWidget);
      await tester.tap(find.text('Last 7 Days'));
      await tester.pumpAndSettle();

      // Test auto-refresh toggle
      await tester.tap(find.byIcon(Icons.sync));
      await tester.pumpAndSettle();

      // Test manual refresh
      await tester.drag(
        find.byType(RefreshIndicator),
        const Offset(0, 300),
      );
      await tester.pumpAndSettle();

      // Test quick actions
      expect(find.text('Submit Issue'), findsOneWidget);
      expect(find.text('View Alerts'), findsOneWidget);
      expect(find.text('Maintenance'), findsOneWidget);
      expect(find.text('Properties'), findsOneWidget);

      // Test navigation back to V1
      await tester.tap(find.text('V1'));
      await tester.pumpAndSettle();

      // Should be back on Dashboard V1
      expect(find.text('V2'), findsOneWidget);
    });

    testWidgets('Authentication Required Flow', (WidgetTester tester) async {
      // Clear authentication
      await serviceLocator.authService.logout();
      
      app.main();
      await tester.pumpAndSettle();

      // Should show login required messaging
      expect(find.text('Login Required'), findsOneWidget);
      expect(find.text('Login'), findsAtLeastNWidget(1));

      // Clicking V2 button should redirect to login
      await tester.tap(find.text('Login').first);
      await tester.pumpAndSettle();

      expect(find.text('Login'), findsOneWidget);
    });
  });
}
