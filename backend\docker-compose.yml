version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: srsr-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: srsr_property_management
      POSTGRES_USER: srsr_user
      POSTGRES_PASSWORD: srsr_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - srsr-network

  # Redis (Optional - for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: srsr-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - srsr-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: srsr-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: **************************************************/srsr_property_management
      JWT_SECRET: your-production-jwt-secret-change-this
      JWT_REFRESH_SECRET: your-production-refresh-secret-change-this
      PORT: 3000
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - srsr-network

  # Nginx (Optional - for reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: srsr-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - srsr-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  srsr-network:
    driver: bridge
