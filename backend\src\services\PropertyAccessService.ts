import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';

export interface PropertyAccessContext {
  userId: string;
  role: UserRole;
  propertyId?: string;
  officeId?: string;
  requestedPath: string;
}

export interface PropertyAccessResult {
  granted: boolean;
  reason?: string;
  assignedProperties: string[];
  assignedOffices: string[];
  metadata?: Record<string, any>;
}

export class PropertyAccessService {
  
  /**
   * Check if user has access to a specific property
   */
  static async checkPropertyAccess(context: PropertyAccessContext): Promise<PropertyAccessResult> {
    try {
      // SUPER_ADMIN has access to all properties
      if (context.role === UserRole.SUPER_ADMIN) {
        const allProperties = await prisma.property.findMany({
          select: { id: true }
        });
        const allOffices = await prisma.office.findMany({
          select: { id: true }
        });

        return {
          granted: true,
          assignedProperties: allProperties.map(p => p.id),
          assignedOffices: allOffices.map(o => o.id),
          reason: 'Super admin access'
        };
      }

      // Get user's property and office assignments
      const user = await prisma.user.findUnique({
        where: { id: context.userId },
        include: {
          propertyAssignments: {
            where: {
              isActive: true,
              OR: [
                { expiresAt: null },
                { expiresAt: { gt: new Date() } }
              ]
            },
            include: { property: true }
          },
          officeAssignments: {
            where: {
              isActive: true,
              OR: [
                { expiresAt: null },
                { expiresAt: { gt: new Date() } }
              ]
            },
            include: { office: true }
          }
        }
      });

      if (!user) {
        return {
          granted: false,
          reason: 'User not found',
          assignedProperties: [],
          assignedOffices: []
        };
      }

      const assignedPropertyIds = user.propertyAssignments.map(pa => pa.propertyId);
      const assignedOfficeIds = user.officeAssignments.map(oa => oa.officeId);

      // Check specific property access
      if (context.propertyId) {
        if (!assignedPropertyIds.includes(context.propertyId)) {
          await this.auditPropertyAccess(context, false, 'Property not assigned to user');
          return {
            granted: false,
            reason: `Access denied: Property ${context.propertyId} not assigned to user`,
            assignedProperties: assignedPropertyIds,
            assignedOffices: assignedOfficeIds
          };
        }
      }

      // Check specific office access
      if (context.officeId) {
        if (!assignedOfficeIds.includes(context.officeId)) {
          await this.auditPropertyAccess(context, false, 'Office not assigned to user');
          return {
            granted: false,
            reason: `Access denied: Office ${context.officeId} not assigned to user`,
            assignedProperties: assignedPropertyIds,
            assignedOffices: assignedOfficeIds
          };
        }
      }

      // Check path-based property access
      const pathPropertyAccess = await this.checkPathPropertyAccess(
        context,
        assignedPropertyIds,
        assignedOfficeIds
      );

      if (!pathPropertyAccess.granted) {
        await this.auditPropertyAccess(context, false, pathPropertyAccess.reason);
        return {
          granted: false,
          reason: pathPropertyAccess.reason,
          assignedProperties: assignedPropertyIds,
          assignedOffices: assignedOfficeIds
        };
      }

      await this.auditPropertyAccess(context, true, 'Property access granted');
      return {
        granted: true,
        assignedProperties: assignedPropertyIds,
        assignedOffices: assignedOfficeIds,
        metadata: {
          propertyCount: assignedPropertyIds.length,
          officeCount: assignedOfficeIds.length
        }
      };

    } catch (error) {
      console.error('Error checking property access:', error);
      return {
        granted: false,
        reason: 'System error during property access check',
        assignedProperties: [],
        assignedOffices: []
      };
    }
  }

  /**
   * Check path-based property access (for paths that don't have explicit propertyId)
   */
  private static async checkPathPropertyAccess(
    context: PropertyAccessContext,
    assignedPropertyIds: string[],
    assignedOfficeIds: string[]
  ): Promise<{ granted: boolean; reason?: string }> {
    
    // Paths that require property assignment
    const propertyPaths = [
      '/properties',
      '/dashboard' // Dashboard shows property data
    ];

    // Paths that require office assignment
    const officePaths = [
      '/office'
    ];

    // Check if path requires property access
    const requiresPropertyAccess = propertyPaths.some(path => 
      context.requestedPath.startsWith(path)
    );

    const requiresOfficeAccess = officePaths.some(path => 
      context.requestedPath.startsWith(path)
    );

    if (requiresPropertyAccess && assignedPropertyIds.length === 0) {
      return {
        granted: false,
        reason: 'Access denied: No properties assigned to user'
      };
    }

    if (requiresOfficeAccess && assignedOfficeIds.length === 0) {
      return {
        granted: false,
        reason: 'Access denied: No offices assigned to user'
      };
    }

    return { granted: true };
  }

  /**
   * Get user's accessible properties with role-based filtering
   */
  static async getUserAccessibleProperties(
    userId: string,
    role: UserRole,
    filters?: {
      propertyType?: string;
      status?: string;
      location?: string;
    }
  ): Promise<any[]> {
    try {
      // SUPER_ADMIN sees all properties
      if (role === UserRole.SUPER_ADMIN) {
        return await prisma.property.findMany({
          where: {
            ...(filters?.propertyType && { type: filters.propertyType }),
            ...(filters?.status && { status: filters.status }),
            ...(filters?.location && { location: { contains: filters.location } })
          },
          include: {
            _count: {
              select: {
                propertyAssignments: true
              }
            }
          }
        });
      }

      // Other roles see only assigned properties
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          propertyAssignments: {
            where: {
              isActive: true,
              OR: [
                { expiresAt: null },
                { expiresAt: { gt: new Date() } }
              ]
            },
            include: {
              property: {
                include: {
                  _count: {
                    select: {
                      propertyAssignments: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!user) return [];

      return user.propertyAssignments
        .map(pa => pa.property)
        .filter(property => {
          if (filters?.propertyType && property.type !== filters.propertyType) return false;
          if (filters?.status && property.status !== filters.status) return false;
          if (filters?.location && !property.location?.includes(filters.location)) return false;
          return true;
        });

    } catch (error) {
      console.error('Error getting accessible properties:', error);
      return [];
    }
  }

  /**
   * Assign property to user
   */
  static async assignPropertyToUser(
    userId: string,
    propertyId: string,
    assignedBy: string,
    options?: {
      roleContext?: string;
      expiresAt?: Date;
      metadata?: Record<string, any>;
    }
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if assignment already exists
      const existingAssignment = await prisma.propertyAssignment.findUnique({
        where: {
          userId_propertyId: {
            userId,
            propertyId
          }
        }
      });

      if (existingAssignment && existingAssignment.isActive) {
        return {
          success: false,
          message: 'User is already assigned to this property'
        };
      }

      // Create or reactivate assignment
      await prisma.propertyAssignment.upsert({
        where: {
          userId_propertyId: {
            userId,
            propertyId
          }
        },
        update: {
          isActive: true,
          assignedBy,
          assignedAt: new Date(),
          expiresAt: options?.expiresAt,
          roleContext: options?.roleContext,
          metadata: options?.metadata || {}
        },
        create: {
          userId,
          propertyId,
          assignedBy,
          roleContext: options?.roleContext,
          expiresAt: options?.expiresAt,
          metadata: options?.metadata || {}
        }
      });

      // Audit the assignment
      await prisma.assignmentAudit.create({
        data: {
          userId,
          resourceType: 'property',
          resourceId: propertyId,
          action: 'assigned',
          performedBy: assignedBy,
          reason: 'Property assigned to user',
          metadata: options?.metadata || {}
        }
      });

      return {
        success: true,
        message: 'Property assigned successfully'
      };

    } catch (error) {
      console.error('Error assigning property:', error);
      return {
        success: false,
        message: 'Failed to assign property'
      };
    }
  }

  /**
   * Remove property assignment from user
   */
  static async unassignPropertyFromUser(
    userId: string,
    propertyId: string,
    performedBy: string,
    reason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      await prisma.propertyAssignment.updateMany({
        where: {
          userId,
          propertyId,
          isActive: true
        },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      });

      // Audit the unassignment
      await prisma.assignmentAudit.create({
        data: {
          userId,
          resourceType: 'property',
          resourceId: propertyId,
          action: 'unassigned',
          performedBy,
          reason: reason || 'Property unassigned from user'
        }
      });

      return {
        success: true,
        message: 'Property unassigned successfully'
      };

    } catch (error) {
      console.error('Error unassigning property:', error);
      return {
        success: false,
        message: 'Failed to unassign property'
      };
    }
  }

  /**
   * Audit property access attempts
   */
  private static async auditPropertyAccess(
    context: PropertyAccessContext,
    granted: boolean,
    reason?: string
  ): Promise<void> {
    try {
      await prisma.navigationAudit.create({
        data: {
          userId: context.userId,
          path: context.requestedPath,
          action: 'property_access',
          granted,
          reason,
          context: {
            propertyId: context.propertyId,
            officeId: context.officeId,
            role: context.role
          }
        }
      });
    } catch (error) {
      console.error('Error auditing property access:', error);
    }
  }
}
