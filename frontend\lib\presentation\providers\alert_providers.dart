import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/alert.dart';
import '../../data/repositories/alert_repository.dart';
import '../../core/services/service_locator.dart';
import '../../core/utils/cache_manager.dart';
import '../../core/utils/connectivity_manager.dart';

// Alert Repository Provider
final alertRepositoryProvider = Provider<AlertRepository>((ref) {
  return serviceLocator.alertRepository;
});

// Alerts Provider with filtering and pagination
final alertsProvider = FutureProvider.family<PaginatedResponse<Alert>, AlertParams>((ref, params) async {
  final repository = ref.read(alertRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'alerts_${params.hashCode}';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getAlerts(
        propertyId: params.propertyId,
        severity: params.severity,
        status: params.status,
        category: params.category,
        page: params.page,
        limit: params.limit,
        sortBy: params.sortBy,
        sortOrder: params.sortOrder,
      );
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 3), // Short cache for alerts
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<PaginatedResponse<Alert>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // If no cache and offline, return empty response
    if (!connectivityManager.isConnected) {
      return PaginatedResponse<Alert>.empty();
    }
    
    throw Exception('Failed to load alerts');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<PaginatedResponse<Alert>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return PaginatedResponse<Alert>.empty();
  }
});

// Single Alert Provider
final alertProvider = FutureProvider.family<Alert?, String>((ref, alertId) async {
  final repository = ref.read(alertRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'alert_$alertId';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getAlert(alertId);
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 10), // Medium cache for single alert
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<Alert>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return null;
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<Alert>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return null;
  }
});

// Alert Statistics Provider
final alertStatisticsProvider = FutureProvider.family<AlertStatistics, AlertStatisticsParams>((ref, params) async {
  final repository = ref.read(alertRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'alert_statistics_${params.hashCode}';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getAlertStatistics(
        propertyId: params.propertyId,
        dateFrom: params.dateFrom,
        dateTo: params.dateTo,
      );
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 15), // Medium cache for statistics
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<AlertStatistics>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // If no cache and offline, return empty statistics
    if (!connectivityManager.isConnected) {
      return AlertStatistics.empty();
    }
    
    throw Exception('Failed to load alert statistics');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<AlertStatistics>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return AlertStatistics.empty();
  }
});

// Create Alert Provider
final createAlertProvider = FutureProvider.family<bool, CreateAlertParams>((ref, params) async {
  final repository = ref.read(alertRepositoryProvider);
  
  try {
    final response = await repository.createAlert(
      propertyId: params.propertyId,
      title: params.title,
      message: params.message,
      severity: params.severity,
      category: params.category,
      metadata: params.metadata,
    );
    
    if (response.success) {
      // Invalidate related providers to refresh data
      ref.invalidate(alertsProvider);
      ref.invalidate(alertStatisticsProvider);
      return true;
    }
    
    return false;
  } catch (e) {
    return false;
  }
});

// Update Alert Status Provider
final updateAlertStatusProvider = FutureProvider.family<bool, UpdateAlertStatusParams>((ref, params) async {
  final repository = ref.read(alertRepositoryProvider);
  
  try {
    final response = await repository.updateAlertStatus(
      alertId: params.alertId,
      status: params.status,
    );
    
    if (response.success) {
      // Invalidate related providers to refresh data
      ref.invalidate(alertsProvider);
      ref.invalidate(alertProvider(params.alertId));
      ref.invalidate(alertStatisticsProvider);
      return true;
    }
    
    return false;
  } catch (e) {
    return false;
  }
});

// Delete Alert Provider
final deleteAlertProvider = FutureProvider.family<bool, String>((ref, alertId) async {
  final repository = ref.read(alertRepositoryProvider);
  
  try {
    final response = await repository.deleteAlert(alertId);
    
    if (response.success) {
      // Invalidate related providers to refresh data
      ref.invalidate(alertsProvider);
      ref.invalidate(alertProvider(alertId));
      ref.invalidate(alertStatisticsProvider);
      return true;
    }
    
    return false;
  } catch (e) {
    return false;
  }
});

// Filtered Alerts Provider (computed from main alerts provider)
final filteredAlertsProvider = Provider.family<List<Alert>, AlertFilterParams>((ref, filterParams) {
  final alertsAsyncValue = ref.watch(alertsProvider(AlertParams(
    propertyId: filterParams.propertyId,
    page: 1,
    limit: 100, // Get more items for local filtering
  )));
  
  return alertsAsyncValue.when(
    data: (paginatedResponse) {
      var alerts = paginatedResponse.data;
      
      // Apply local filters
      if (filterParams.severity != null) {
        alerts = alerts.where((alert) => alert.severity == filterParams.severity).toList();
      }
      
      if (filterParams.status != null) {
        alerts = alerts.where((alert) => alert.status == filterParams.status).toList();
      }
      
      if (filterParams.category != null) {
        alerts = alerts.where((alert) => alert.category == filterParams.category).toList();
      }
      
      if (filterParams.searchQuery != null && filterParams.searchQuery!.isNotEmpty) {
        final query = filterParams.searchQuery!.toLowerCase();
        alerts = alerts.where((alert) => 
          alert.title.toLowerCase().contains(query) ||
          alert.message.toLowerCase().contains(query)
        ).toList();
      }
      
      return alerts;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// State Providers for UI filters
final alertSeverityFilterProvider = StateProvider<String?>((ref) => null);
final alertStatusFilterProvider = StateProvider<String?>((ref) => null);
final alertCategoryFilterProvider = StateProvider<String?>((ref) => null);
final alertSearchQueryProvider = StateProvider<String>((ref) => '');

// Alert Parameters Classes
class AlertParams {
  final String? propertyId;
  final String? severity;
  final String? status;
  final String? category;
  final int page;
  final int limit;
  final String sortBy;
  final String sortOrder;

  const AlertParams({
    this.propertyId,
    this.severity,
    this.status,
    this.category,
    this.page = 1,
    this.limit = 20,
    this.sortBy = 'createdAt',
    this.sortOrder = 'desc',
  });

  @override
  int get hashCode => Object.hash(
    propertyId, severity, status, category, page, limit, sortBy, sortOrder
  );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AlertParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          severity == other.severity &&
          status == other.status &&
          category == other.category &&
          page == other.page &&
          limit == other.limit &&
          sortBy == other.sortBy &&
          sortOrder == other.sortOrder;
}

class AlertStatisticsParams {
  final String? propertyId;
  final String? dateFrom;
  final String? dateTo;

  const AlertStatisticsParams({
    this.propertyId,
    this.dateFrom,
    this.dateTo,
  });

  @override
  int get hashCode => Object.hash(propertyId, dateFrom, dateTo);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AlertStatisticsParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          dateFrom == other.dateFrom &&
          dateTo == other.dateTo;
}

class CreateAlertParams {
  final String? propertyId;
  final String title;
  final String message;
  final String severity;
  final String category;
  final Map<String, dynamic>? metadata;

  const CreateAlertParams({
    this.propertyId,
    required this.title,
    required this.message,
    required this.severity,
    required this.category,
    this.metadata,
  });
}

class UpdateAlertStatusParams {
  final String alertId;
  final String status;

  const UpdateAlertStatusParams({
    required this.alertId,
    required this.status,
  });
}

class AlertFilterParams {
  final String? propertyId;
  final String? severity;
  final String? status;
  final String? category;
  final String? searchQuery;

  const AlertFilterParams({
    this.propertyId,
    this.severity,
    this.status,
    this.category,
    this.searchQuery,
  });
}
