import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../main/main_navigation_screen.dart';

class PropertiesScreen extends ConsumerStatefulWidget {
  const PropertiesScreen({super.key});

  @override
  ConsumerState<PropertiesScreen> createState() => _PropertiesScreenState();
}

class _PropertiesScreenState extends ConsumerState<PropertiesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedFilter = 'all';

  final List<String> _filterOptions = ['all', 'residential', 'office', 'construction'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Properties',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).primaryColor,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(text: 'Residential'),
                Tab(text: 'Office'),
                Tab(text: 'Construction'),
              ],
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildResidentialProperties(),
                _buildOfficeProperties(),
                _buildConstructionSites(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Add new property
          _showAddPropertyDialog();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildResidentialProperties() {
    final properties = [
      {
        'id': '0db5da21-3805-40f6-a04d-fe3acf061955',
        'name': 'Jubilee Hills Residence',
        'address': 'Road No. 36, Jubilee Hills, Hyderabad',
        'status': 'operational',
        'systems': 6,
        'lastUpdated': '2 hours ago',
        'image': 'assets/images/jublee_hills.jpg',
      },
      {
        'id': '800a3cb7-bd72-4b8f-8d92-9fe3c08e91ee',
        'name': 'Gandipet Construction Site',
        'address': 'Gandipet, Hyderabad',
        'status': 'warning',
        'systems': 5,
        'lastUpdated': '1 hour ago',
        'image': 'assets/images/gandipet.jpg',
      },
    ];

    return _buildPropertyList(properties);
  }

  Widget _buildOfficeProperties() {
    final properties = [
      {
        'id': 'a1c9bff1-80bb-44be-a94d-d9dca5937d0f',
        'name': 'Banjara Hills Office Complex',
        'address': 'Banjara Hills, Hyderabad',
        'status': 'operational',
        'systems': 4,
        'lastUpdated': '30 minutes ago',
        'image': 'assets/images/brane_office.jpg',
      },
    ];

    return _buildPropertyList(properties);
  }

  Widget _buildConstructionSites() {
    final sites = [
      {
        'id': 'gandipet-1',
        'name': 'Gandipet 1',
        'address': 'Gandipet, Hyderabad',
        'status': 'operational',
        'workers': 10,
        'lastUpdated': '1 hour ago',
        'progress': 75,
      },
      {
        'id': 'gandipet-2',
        'name': 'Gandipet 2',
        'address': 'Gandipet, Hyderabad',
        'status': 'operational',
        'workers': 8,
        'lastUpdated': '2 hours ago',
        'progress': 60,
      },
      {
        'id': 'gandipet-3-4',
        'name': 'Gandipet 3 & 4',
        'address': 'Gandipet, Hyderabad',
        'status': 'warning',
        'workers': 15,
        'lastUpdated': '3 hours ago',
        'progress': 45,
      },
      {
        'id': 'bachupally',
        'name': 'Bachupally',
        'address': 'Bachupally, Hyderabad',
        'status': 'critical',
        'workers': 12,
        'lastUpdated': '4 hours ago',
        'progress': 30,
      },
    ];

    return _buildConstructionList(sites);
  }

  Widget _buildPropertyList(List<Map<String, dynamic>> properties) {
    final filteredProperties = properties.where((property) {
      if (_searchQuery.isNotEmpty) {
        return property['name']
            .toString()
            .toLowerCase()
            .contains(_searchQuery.toLowerCase());
      }
      return true;
    }).toList();

    return RefreshIndicator(
      onRefresh: () async {
        // TODO: Implement refresh
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredProperties.length,
        itemBuilder: (context, index) {
          final property = filteredProperties[index];
          return _buildPropertyCard(property);
        },
      ),
    );
  }

  Widget _buildPropertyCard(Map<String, dynamic> property) {
    Color statusColor;
    switch (property['status']) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          context.go('/properties/${property['id']}');
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Image
            Container(
              height: 150,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).primaryColor.withValues(alpha: 0.8),
                    Theme.of(context).primaryColor,
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Placeholder for property image
                  Center(
                    child: Icon(
                      Icons.business,
                      size: 60,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                  
                  // Status Badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        property['status'].toString().toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Property Details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Property Name
                  Text(
                    property['name'],
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Address
                  Text(
                    property['address'],
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Systems and Last Updated
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.settings,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${property['systems']} systems',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      Text(
                        'Updated ${property['lastUpdated']}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConstructionList(List<Map<String, dynamic>> sites) {
    return RefreshIndicator(
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sites.length,
        itemBuilder: (context, index) {
          final site = sites[index];
          return _buildConstructionCard(site);
        },
      ),
    );
  }

  Widget _buildConstructionCard(Map<String, dynamic> site) {
    Color statusColor;
    switch (site['status']) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to construction site detail
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          site['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          site['address'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: site['status'], showLabel: true),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progress',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${site['progress']}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: site['progress'] / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Workers and Last Updated
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.people,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${site['workers']} workers',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  Text(
                    'Updated ${site['lastUpdated']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Properties'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter property name...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Properties'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _filterOptions.map((option) {
            return RadioListTile<String>(
              title: Text(option.toUpperCase()),
              value: option,
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showAddPropertyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Property'),
        content: const Text('This feature will be available soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
