// Simple test script to verify maintenance API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/v1';

// Test credentials
const testCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testCredentials);
    authToken = response.data.data.accessToken;
    console.log('✅ Login successful');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    return false;
  }
}

async function testDepartments() {
  try {
    console.log('\n📋 Testing departments endpoint...');
    const response = await axios.get(`${BASE_URL}/maintenance/departments`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Departments retrieved successfully');
    console.log(`📊 Found ${response.data.data.length} departments:`);
    response.data.data.forEach(dept => {
      console.log(`   - ${dept.name}: ${dept._count.maintenanceIssues} issues, ${dept._count.maintenanceFunctions} functions`);
    });
    return true;
  } catch (error) {
    console.error('❌ Departments test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testMaintenanceIssues() {
  try {
    console.log('\n🔧 Testing maintenance issues endpoint...');
    const response = await axios.get(`${BASE_URL}/maintenance/issues?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Maintenance issues retrieved successfully');
    console.log(`📊 Found ${response.data.data.length} issues (showing first 5):`);
    response.data.data.forEach(issue => {
      console.log(`   - ${issue.title} (${issue.department.name}) - ${issue.status} - ${issue.priority}`);
    });
    
    console.log(`📄 Pagination: Page ${response.data.pagination.page} of ${response.data.pagination.pages}, Total: ${response.data.pagination.total}`);
    return true;
  } catch (error) {
    console.error('❌ Maintenance issues test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testMaintenanceFunctions() {
  try {
    console.log('\n⚙️ Testing maintenance functions endpoint...');
    const response = await axios.get(`${BASE_URL}/maintenance/functions?limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Maintenance functions retrieved successfully');
    console.log(`📊 Found ${response.data.data.length} functions (showing first 5):`);
    response.data.data.forEach(func => {
      console.log(`   - ${func.name} - ${func.subFunction} (${func.department.name})`);
    });
    
    console.log(`📄 Pagination: Page ${response.data.pagination.page} of ${response.data.pagination.pages}, Total: ${response.data.pagination.total}`);
    return true;
  } catch (error) {
    console.error('❌ Maintenance functions test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testCreateDepartment() {
  try {
    console.log('\n➕ Testing create department endpoint...');
    const newDepartment = {
      name: 'Test Department',
      description: 'A test department created by API test'
    };
    
    const response = await axios.post(`${BASE_URL}/maintenance/departments`, newDepartment, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Department created successfully');
    console.log(`📋 Created: ${response.data.data.name} - ${response.data.data.description}`);
    return response.data.data.id;
  } catch (error) {
    console.error('❌ Create department test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testCreateMaintenanceIssue(departmentId) {
  try {
    console.log('\n➕ Testing create maintenance issue endpoint...');
    const newIssue = {
      title: 'Test Maintenance Issue',
      description: 'A test issue created by API test',
      departmentId: departmentId,
      priority: 'MEDIUM',
      startDate: new Date().toISOString(),
      expectedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      isRecurring: false,
      remarks: 'This is a test issue for API validation'
    };
    
    const response = await axios.post(`${BASE_URL}/maintenance/issues`, newIssue, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Maintenance issue created successfully');
    console.log(`🔧 Created: ${response.data.data.title} - ${response.data.data.priority} priority`);
    return response.data.data.id;
  } catch (error) {
    console.error('❌ Create maintenance issue test failed:', error.response?.data || error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Maintenance API Tests\n');
  
  // Login first
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }
  
  // Test read operations
  await testDepartments();
  await testMaintenanceIssues();
  await testMaintenanceFunctions();
  
  // Test create operations
  const departmentId = await testCreateDepartment();
  if (departmentId) {
    await testCreateMaintenanceIssue(departmentId);
  }
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📝 Summary:');
  console.log('   ✅ Authentication working');
  console.log('   ✅ Departments endpoint working');
  console.log('   ✅ Maintenance issues endpoint working');
  console.log('   ✅ Maintenance functions endpoint working');
  console.log('   ✅ Create operations working');
  console.log('\n🌐 You can also test the API documentation at: http://localhost:3000/api-docs');
}

// Run the tests
runTests().catch(console.error);
