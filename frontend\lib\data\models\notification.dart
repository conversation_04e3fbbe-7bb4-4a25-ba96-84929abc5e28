import 'package:json_annotation/json_annotation.dart';

part 'notification.g.dart';

@JsonSerializable()
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final String priority;
  final bool isRead;
  final String? propertyId;
  final PropertyInfo? property;
  final String createdAt;
  final String? scheduledFor;
  final String? expiresAt;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.isRead,
    this.propertyId,
    this.property,
    required this.createdAt,
    this.scheduledFor,
    this.expiresAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) => _$NotificationModelFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    String? priority,
    bool? isRead,
    String? propertyId,
    PropertyInfo? property,
    String? createdAt,
    String? scheduledFor,
    String? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      propertyId: propertyId ?? this.propertyId,
      property: property ?? this.property,
      createdAt: createdAt ?? this.createdAt,
      scheduledFor: scheduledFor ?? this.scheduledFor,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  // Helper getters
  bool get isInfo => type == 'INFO';
  bool get isWarning => type == 'WARNING';
  bool get isError => type == 'ERROR';
  bool get isSuccess => type == 'SUCCESS';
  
  bool get isLowPriority => priority == 'LOW';
  bool get isMediumPriority => priority == 'MEDIUM';
  bool get isHighPriority => priority == 'HIGH';
  bool get isUrgent => priority == 'URGENT';
  
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime? get scheduledDateTime => scheduledFor != null ? DateTime.parse(scheduledFor!) : null;
  DateTime? get expiresDateTime => expiresAt != null ? DateTime.parse(expiresAt!) : null;
  
  Duration get age => DateTime.now().difference(createdDateTime);
  
  bool get isExpired => expiresDateTime != null && DateTime.now().isAfter(expiresDateTime!);
  bool get isScheduled => scheduledDateTime != null && DateTime.now().isBefore(scheduledDateTime!);
  bool get isActive => !isExpired && !isScheduled;
}

@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String type;

  const PropertyInfo({
    required this.id,
    required this.name,
    required this.type,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

@JsonSerializable()
class CreateNotificationRequest {
  final String title;
  final String message;
  final String type;
  final String? priority;
  final List<String>? targetUsers;
  final List<String>? targetRoles;
  final String? propertyId;
  final String? scheduledFor;
  final String? expiresAt;

  const CreateNotificationRequest({
    required this.title,
    required this.message,
    required this.type,
    this.priority,
    this.targetUsers,
    this.targetRoles,
    this.propertyId,
    this.scheduledFor,
    this.expiresAt,
  });

  factory CreateNotificationRequest.fromJson(Map<String, dynamic> json) => _$CreateNotificationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateNotificationRequestToJson(this);
}

@JsonSerializable()
class NotificationQueryParams {
  final int? page;
  final int? limit;
  final String? type;
  final String? priority;
  final bool? isRead;
  final String? propertyId;

  const NotificationQueryParams({
    this.page,
    this.limit,
    this.type,
    this.priority,
    this.isRead,
    this.propertyId,
  });

  factory NotificationQueryParams.fromJson(Map<String, dynamic> json) => _$NotificationQueryParamsFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationQueryParamsToJson(this);

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{};
    if (page != null) params['page'] = page.toString();
    if (limit != null) params['limit'] = limit.toString();
    if (type != null) params['type'] = type;
    if (priority != null) params['priority'] = priority;
    if (isRead != null) params['isRead'] = isRead.toString();
    if (propertyId != null) params['propertyId'] = propertyId;
    return params;
  }
}

@JsonSerializable()
class UnreadCountResponse {
  final int unreadCount;

  const UnreadCountResponse({
    required this.unreadCount,
  });

  factory UnreadCountResponse.fromJson(Map<String, dynamic> json) => _$UnreadCountResponseFromJson(json);
  Map<String, dynamic> toJson() => _$UnreadCountResponseToJson(this);
}

@JsonSerializable()
class MarkAllReadResponse {
  final int markedCount;

  const MarkAllReadResponse({
    required this.markedCount,
  });

  factory MarkAllReadResponse.fromJson(Map<String, dynamic> json) => _$MarkAllReadResponseFromJson(json);
  Map<String, dynamic> toJson() => _$MarkAllReadResponseToJson(this);
}

// Notification statistics
@JsonSerializable()
class NotificationStatistics {
  final int total;
  final int unread;
  final int read;
  final int info;
  final int warning;
  final int error;
  final int success;
  final int low;
  final int medium;
  final int high;
  final int urgent;

  const NotificationStatistics({
    required this.total,
    required this.unread,
    required this.read,
    required this.info,
    required this.warning,
    required this.error,
    required this.success,
    required this.low,
    required this.medium,
    required this.high,
    required this.urgent,
  });

  factory NotificationStatistics.fromJson(Map<String, dynamic> json) => _$NotificationStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationStatisticsToJson(this);

  // Helper getters
  double get readPercentage => total > 0 ? (read / total) * 100 : 0;
  double get unreadPercentage => total > 0 ? (unread / total) * 100 : 0;
  
  double get infoPercentage => total > 0 ? (info / total) * 100 : 0;
  double get warningPercentage => total > 0 ? (warning / total) * 100 : 0;
  double get errorPercentage => total > 0 ? (error / total) * 100 : 0;
  double get successPercentage => total > 0 ? (success / total) * 100 : 0;
  
  double get urgentPercentage => total > 0 ? (urgent / total) * 100 : 0;
  double get highPercentage => total > 0 ? (high / total) * 100 : 0;
  double get mediumPercentage => total > 0 ? (medium / total) * 100 : 0;
  double get lowPercentage => total > 0 ? (low / total) * 100 : 0;
}
