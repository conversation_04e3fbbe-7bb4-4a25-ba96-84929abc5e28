import { PrismaClient, UserRole } from '@prisma/client';
import { SECURITY_PERMISSIONS, ELECTRICITY_PERMISSIONS, WATER_PERMISSIONS } from '../config/granular-permissions';

const prisma = new PrismaClient();

async function seedGranularPermissions() {
  console.log('🌱 Seeding granular permissions...');

  try {
    // Create roles if they don't exist
    const roles = await Promise.all([
      prisma.role.upsert({
        where: { name: 'SUPER_ADMIN' },
        update: {},
        create: {
          name: 'SUPER_ADMIN',
          displayName: 'Super Administrator',
          description: 'Full system access',
          level: 100,
        },
      }),
      prisma.role.upsert({
        where: { name: 'PROPERTY_MANAGER' },
        update: {},
        create: {
          name: 'PROPERTY_MANAGER',
          displayName: 'Property Manager',
          description: 'Manages properties and systems',
          level: 80,
        },
      }),
      prisma.role.upsert({
        where: { name: 'SECURITY_PERSONNEL' },
        update: {},
        create: {
          name: 'SECURITY_PERSONNEL',
          displayName: 'Security Personnel',
          description: 'Security operations and monitoring',
          level: 60,
        },
      }),
      prisma.role.upsert({
        where: { name: 'MAINTENANCE_STAFF' },
        update: {},
        create: {
          name: 'MAINTENANCE_STAFF',
          displayName: 'Maintenance Staff',
          description: 'Maintenance and repairs',
          level: 60,
        },
      }),
      prisma.role.upsert({
        where: { name: 'OFFICE_MANAGER' },
        update: {},
        create: {
          name: 'OFFICE_MANAGER',
          displayName: 'Office Manager',
          description: 'Office operations management',
          level: 70,
        },
      }),
    ]);

    console.log('✅ Roles created/updated');

    // Seed Security Management Permissions
    await seedScreenPermissions('security_management', SECURITY_PERMISSIONS);
    
    // Seed Electricity Management Permissions
    await seedScreenPermissions('electricity_management', ELECTRICITY_PERMISSIONS);
    
    // Seed Water Management Permissions
    await seedScreenPermissions('water_management', WATER_PERMISSIONS);

    console.log('🎉 Granular permissions seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function seedScreenPermissions(screenName: string, screenPermissions: any) {
  console.log(`📋 Seeding ${screenName} permissions...`);

  for (const [roleName, permissions] of Object.entries(screenPermissions)) {
    const role = await prisma.role.findUnique({
      where: { name: roleName as UserRole },
    });

    if (!role) {
      console.warn(`⚠️ Role ${roleName} not found, skipping...`);
      continue;
    }

    // Create tab permissions
    for (const [tabId, tabPermission] of Object.entries(permissions.tabs)) {
      const tabPerm = tabPermission as any;
      
      await prisma.tabPermission.upsert({
        where: {
          screen_tabId_roleId: {
            screen: screenName,
            tabId: tabId,
            roleId: role.id,
          },
        },
        update: {
          accessLevel: tabPerm.accessLevel,
          isVisible: tabPerm.accessLevel !== 'none',
          isEnabled: tabPerm.accessLevel === 'full',
          restrictions: tabPerm.restrictions || {},
        },
        create: {
          screen: screenName,
          tabId: tabId,
          tabName: tabPerm.tabName,
          roleId: role.id,
          accessLevel: tabPerm.accessLevel,
          isVisible: tabPerm.accessLevel !== 'none',
          isEnabled: tabPerm.accessLevel === 'full',
          restrictions: tabPerm.restrictions || {},
        },
      });

      // Create UI component permissions for each tab
      if (tabPerm.restrictions) {
        await createUIComponentPermissions(
          screenName,
          tabId,
          role.id,
          tabPerm.accessLevel,
          tabPerm.restrictions
        );
      }
    }
  }

  console.log(`✅ ${screenName} permissions seeded`);
}

async function createUIComponentPermissions(
  screen: string,
  tab: string,
  roleId: string,
  accessLevel: string,
  restrictions: any
) {
  // Create component permissions based on restrictions
  const components = getComponentsForTab(screen, tab);

  for (const component of components) {
    const isVisible = !restrictions.hideFields?.includes(component.field);
    const isEnabled = !restrictions.disableActions?.includes(component.action) && accessLevel !== 'read_only';

    await prisma.uIComponentPermission.upsert({
      where: {
        componentId_roleId: {
          componentId: component.id,
          roleId: roleId,
        },
      },
      update: {
        accessLevel,
        isVisible,
        isEnabled,
        restrictions: restrictions,
      },
      create: {
        componentId: component.id,
        screen,
        tab,
        widget: component.widget,
        roleId,
        accessLevel,
        isVisible,
        isEnabled,
        restrictions: restrictions,
      },
    });
  }
}

function getComponentsForTab(screen: string, tab: string) {
  // Define components for each screen/tab combination
  const componentMap: Record<string, Record<string, any[]>> = {
    security_management: {
      overview: [
        { id: 'security_management.overview.system_status', widget: 'system_status_card', field: 'system_status', action: 'view' },
        { id: 'security_management.overview.recent_alerts', widget: 'recent_alerts_card', field: 'alerts', action: 'view' },
        { id: 'security_management.overview.financial_summary', widget: 'financial_summary_card', field: 'financial_data', action: 'view' },
      ],
      cctv: [
        { id: 'security_management.cctv.camera_grid', widget: 'camera_grid', field: 'live_feeds', action: 'view' },
        { id: 'security_management.cctv.camera_controls', widget: 'camera_controls', field: 'camera_control', action: 'control' },
        { id: 'security_management.cctv.recordings', widget: 'recordings', field: 'recordings', action: 'view' },
        { id: 'security_management.cctv.add_camera', widget: 'add_camera_button', field: 'camera_management', action: 'add_camera' },
      ],
      access_control: [
        { id: 'security_management.access_control.system_status', widget: 'access_system_status', field: 'system_status', action: 'view' },
        { id: 'security_management.access_control.user_management', widget: 'user_management', field: 'user_codes', action: 'manage_users' },
        { id: 'security_management.access_control.access_logs', widget: 'access_logs', field: 'access_logs', action: 'view' },
        { id: 'security_management.access_control.emergency_override', widget: 'emergency_override', field: 'emergency_control', action: 'emergency_override' },
      ],
      maintenance: [
        { id: 'security_management.maintenance.schedule', widget: 'maintenance_schedule', field: 'maintenance_schedule', action: 'view' },
        { id: 'security_management.maintenance.history', widget: 'maintenance_history', field: 'maintenance_history', action: 'view' },
        { id: 'security_management.maintenance.costs', widget: 'maintenance_costs', field: 'maintenance_cost', action: 'view' },
      ],
      contacts: [
        { id: 'security_management.contacts.emergency', widget: 'emergency_contacts', field: 'emergency_contacts', action: 'view' },
        { id: 'security_management.contacts.vendors', widget: 'vendor_contacts', field: 'vendor_contacts', action: 'view' },
        { id: 'security_management.contacts.contracts', widget: 'contract_details', field: 'contract_details', action: 'view' },
      ],
    },
    electricity_management: {
      overview: [
        { id: 'electricity_management.overview.system_status', widget: 'system_status', field: 'system_status', action: 'view' },
        { id: 'electricity_management.overview.cost_analysis', widget: 'cost_analysis', field: 'cost_analysis', action: 'view' },
      ],
      generator: [
        { id: 'electricity_management.generator.fuel_level', widget: 'fuel_level', field: 'fuel_level', action: 'view' },
        { id: 'electricity_management.generator.update_fuel', widget: 'update_fuel_button', field: 'fuel_management', action: 'update_fuel' },
        { id: 'electricity_management.generator.start_generator', widget: 'start_generator_button', field: 'generator_control', action: 'start_generator' },
      ],
      ups: [
        { id: 'electricity_management.ups.battery_status', widget: 'battery_status', field: 'battery_status', action: 'view' },
        { id: 'electricity_management.ups.diagnostics', widget: 'diagnostics_button', field: 'diagnostics', action: 'run_diagnostics' },
      ],
    },
  };

  return componentMap[screen]?.[tab] || [];
}

// Run the seeding
if (require.main === module) {
  seedGranularPermissions()
    .catch((error) => {
      console.error('Failed to seed permissions:', error);
      process.exit(1);
    });
}

export { seedGranularPermissions };
