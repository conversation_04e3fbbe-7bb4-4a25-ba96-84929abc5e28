const MaintenanceIssue = require('../models/MaintenanceIssue');
const MaintenanceAttachment = require('../models/MaintenanceAttachment');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const mongoose = require('mongoose');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/maintenance');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `maintenance-${uniqueSuffix}${ext}`);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  // Allowed file types
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, PDFs, and documents are allowed.'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Maximum 5 files per request
  }
});

// Upload attachment to maintenance issue
exports.uploadAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    // Check if issue exists
    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && 
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to upload attachments for this issue'
        });
      }
    }

    // Use multer middleware
    upload.array('files', 5)(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              message: 'File size too large. Maximum size is 10MB per file.'
            });
          }
          if (err.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
              success: false,
              message: 'Too many files. Maximum 5 files per upload.'
            });
          }
        }
        return res.status(400).json({
          success: false,
          message: err.message || 'File upload failed'
        });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No files uploaded'
        });
      }

      try {
        const attachments = [];

        // Process each uploaded file
        for (const file of req.files) {
          // Generate file URL
          const fileUrl = `/uploads/maintenance/${file.filename}`;

          // Create attachment record
          const attachment = new MaintenanceAttachment({
            issueId: id,
            fileName: file.filename,
            originalName: file.originalname,
            fileType: file.mimetype,
            fileSize: file.size,
            filePath: file.path,
            url: fileUrl,
            uploadedBy: req.user.id
          });

          await attachment.save();

          // Add to issue's attachments array
          issue.attachments.push(attachment._id);

          // Populate uploader info
          await attachment.populate('uploader', 'name email');

          attachments.push(attachment);
        }

        // Save issue with new attachments
        await issue.save();

        // Add activity
        await issue.addActivity('ATTACHMENT_ADDED', req.user.id);

        res.status(201).json({
          success: true,
          data: attachments,
          message: `${attachments.length} file(s) uploaded successfully`
        });
      } catch (error) {
        // Clean up uploaded files on error
        for (const file of req.files) {
          try {
            await fs.unlink(file.path);
          } catch (unlinkError) {
            console.error('Error deleting file:', unlinkError);
          }
        }

        console.error('Error saving attachments:', error);
        res.status(500).json({
          success: false,
          message: 'Failed to save attachments',
          error: error.message
        });
      }
    });
  } catch (error) {
    console.error('Error uploading attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload attachment',
      error: error.message
    });
  }
};

// Get attachments for maintenance issue
exports.getAttachments = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    // Check if issue exists and user has access
    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && 
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to view attachments for this issue'
        });
      }
    }

    const attachments = await MaintenanceAttachment.find({ issueId: id })
      .populate('uploader', 'name email')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: attachments
    });
  } catch (error) {
    console.error('Error fetching attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachments',
      error: error.message
    });
  }
};

// Delete attachment
exports.deleteAttachment = async (req, res) => {
  try {
    const { id, attachmentId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(attachmentId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid ID'
      });
    }

    const attachment = await MaintenanceAttachment.findById(attachmentId);
    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Check if attachment belongs to the issue
    if (attachment.issueId.toString() !== id) {
      return res.status(400).json({
        success: false,
        message: 'Attachment does not belong to this issue'
      });
    }

    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check permissions - only admin, uploader, or issue reporter can delete
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN' && 
        attachment.uploadedBy.toString() !== req.user.id &&
        issue.reportedBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to delete this attachment'
      });
    }

    // Delete file from filesystem
    try {
      await fs.unlink(attachment.filePath);
    } catch (fileError) {
      console.error('Error deleting file:', fileError);
      // Continue with database deletion even if file deletion fails
    }

    // Remove from issue's attachments array
    issue.attachments = issue.attachments.filter(
      attachmentRef => attachmentRef.toString() !== attachmentId
    );
    await issue.save();

    // Delete attachment record
    await MaintenanceAttachment.findByIdAndDelete(attachmentId);

    res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete attachment',
      error: error.message
    });
  }
};

// Download attachment
exports.downloadAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(attachmentId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid attachment ID'
      });
    }

    const attachment = await MaintenanceAttachment.findById(attachmentId);
    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Check if user has access to the issue
    const issue = await MaintenanceIssue.findById(attachment.issueId);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Associated maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && 
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to download this attachment'
        });
      }
    }

    // Check if file exists
    try {
      await fs.access(attachment.filePath);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: 'File not found on server'
      });
    }

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`);
    res.setHeader('Content-Type', attachment.fileType);

    // Send file
    res.sendFile(path.resolve(attachment.filePath));
  } catch (error) {
    console.error('Error downloading attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download attachment',
      error: error.message
    });
  }
};
