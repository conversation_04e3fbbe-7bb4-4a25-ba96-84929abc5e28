import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../main/main_navigation_screen.dart';

class SecurityManagementScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const SecurityManagementScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<SecurityManagementScreen> createState() => _SecurityManagementScreenState();
}

class _SecurityManagementScreenState extends ConsumerState<SecurityManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCamera = 'Camera 1 - Front Gate facing road';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Security',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Header with icon and title
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.security,
                    color: Colors.green,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Security',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Tab Bar
          Container(
            color: Colors.grey[100],
            child: TabBar(
              controller: _tabController,
              indicatorColor: AppTheme.primaryColor,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: Colors.grey[600],
              tabs: const [
                Tab(text: 'Overview'),
                Tab(text: 'CCTV'),
                Tab(text: 'Security Details'),
                Tab(text: 'Security Guard Logs'),
              ],
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildCCTVTab(),
                _buildSecurityDetailsTab(),
                _buildSecurityGuardLogsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overview Header
          Row(
            children: [
              const Icon(Icons.info_outline, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Security Overview',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'General information about security systems for Jublee Hills Home',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Number of Security Guards
          _buildOverviewSection(
            'Number of Security Guards:',
            null,
            children: [
              _buildSpecItem('1 guard per shift', 'near the gate (12-hour shifts)'),
            ],
          ),

          const SizedBox(height: 16),

          // CCTV Cameras
          _buildOverviewSection(
            'CCTV Cameras:',
            null,
            children: [
              _buildSpecItem('11 cameras', 'installed at home'),
              const SizedBox(height: 8),
              const Text(
                'CCTV systems connected to UPS/Generator',
                style: TextStyle(
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Access Control System
          _buildOverviewSection(
            'Access Control System:',
            null,
            children: [
              _buildSpecItem('Passcode Type.', 'One at the bedroom door and One at the door in the Garage.'),
              const SizedBox(height: 8),
              const Text(
                'Batteries to checked monthly and replaced when capacity is less than 50%',
                style: TextStyle(
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Entry/Exit Logs
          _buildOverviewSection(
            'Entry/Exit Logs:',
            'Maintained by security at the Gate',
          ),

          const SizedBox(height: 16),

          // Records of Workers
          _buildOverviewSection(
            'Records of Workers:',
            'Maintained by Ramana Prasad',
          ),
        ],
      ),
    );
  }

  Widget _buildCCTVTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // CCTV System Header
          Row(
            children: [
              const Icon(Icons.videocam, size: 20),
              const SizedBox(width: 8),
              const Text(
                'CCTV System',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'CCTV system information and status',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // System Status
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      'System Status: Operational',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      'All cameras are functioning normally.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // CCTV Locations
          const Text(
            'CCTV Locations at Jublee Hills Home',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 16),

          // Camera locations diagram placeholder
          Container(
            height: 300,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  child: const Text(
                    'CCTV Locations at\nJublee Hills Home',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Center(
                      child: Text(
                        'Property Layout with Camera Positions\n(Interactive diagram would be here)',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Camera list
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Camera Locations:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 12),
                ..._buildCameraList(),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Cloud Access
          const Text(
            'Cloud Access',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Access the CCTV footage remotely through the TrueView cloud service. Login credentials are available from the security supervisor.',
            style: TextStyle(
              fontSize: 14,
              height: 1.4,
            ),
          ),

          const SizedBox(height: 16),

          ElevatedButton.icon(
            onPressed: () {
              // Handle TrueView Cloud Access
            },
            icon: const Icon(Icons.cloud),
            label: const Text('TrueView Cloud Access'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),

          const SizedBox(height: 24),

          // Camera Selection
          const Text(
            'Camera Selection',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 16),

          const Text(
            'Select Camera',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 8),

          DropdownButtonFormField<String>(
            value: _selectedCamera,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: _getCameraOptions().map((camera) {
              return DropdownMenuItem<String>(
                value: camera,
                child: Text(camera),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCamera = value!;
              });
            },
          ),

          const SizedBox(height: 24),

          // Live Feed Placeholder
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Camera 1 Live Feed',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8),
                  Icon(
                    Icons.videocam,
                    color: Colors.white,
                    size: 48,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Live indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Text(
              'Live: Camera 1',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // CCTV System Details
          const Text(
            'CCTV System Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                _buildDetailRow('Total cameras:', '11'),
                _buildDetailRow('Recording Capacity:', '30 days'),
                _buildDetailRow('Resolution:', '5 Mega Pixel'),
                _buildDetailRow('Motion Detection:', 'Enabled'),
                _buildDetailRow('Next Maintenance Date:', 'May 15th, 2026'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Security Personnel Header
          Row(
            children: [
              const Icon(Icons.people, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Security Personnel',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Security staff information',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Security Guards
          Row(
            children: [
              Expanded(
                child: _buildSecurityPersonCard(
                  'Bhudev Kumar',
                  'Day Shift Security',
                  '9392892446',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSecurityPersonCard(
                  'Vikas Kumar',
                  'Night Shift Security',
                  '7707067414',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityGuardLogsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Security Guard Logs Header
          Row(
            children: [
              const Icon(Icons.assignment, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Security Guard Logs',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Daily security logs and reports',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Coming Soon placeholder
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Center(
              child: Column(
                children: [
                  Icon(
                    Icons.assignment_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Security Guard Logs',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Daily logs and incident reports will be displayed here',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Widget _buildOverviewSection(String title, String? subtitle, {List<Widget>? children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              height: 1.4,
            ),
          ),
        ],
        if (children != null) ...children,
      ],
    );
  }

  Widget _buildSpecItem(String label, String value) {
    return RichText(
      text: TextSpan(
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black,
          height: 1.4,
        ),
        children: [
          TextSpan(
            text: label,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          TextSpan(text: ' $value'),
        ],
      ),
    );
  }

  List<Widget> _buildCameraList() {
    final cameras = [
      '1. Front Gate facing road',
      '2. Front Gate facing Security',
      '3. Front Gate facing main gate',
      '4. Library Door',
      '5. Garage, near the entry door',
      '6. Left Corridor',
      '7. Garden facing Back Door/Verandah',
      '8. Garden facing Back Gate',
      '9. Garden facing Kitchen',
      '10. Near Kitchen Door',
      '11. Pool',
    ];

    return cameras.map((camera) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Text(
          camera,
          style: const TextStyle(
            fontSize: 14,
            height: 1.4,
          ),
        ),
      );
    }).toList();
  }

  List<String> _getCameraOptions() {
    return [
      'Camera 1 - Front Gate facing road',
      'Camera 2 - Front Gate facing Security',
      'Camera 3 - Front Gate facing main gate',
      'Camera 4 - Library Door',
      'Camera 5 - Garage, near the entry door',
      'Camera 6 - Left Corridor',
      'Camera 7 - Garden facing Back Door/Verandah',
      'Camera 8 - Garden facing Back Gate',
      'Camera 9 - Garden facing Kitchen',
      'Camera 10 - Near Kitchen Door',
      'Camera 11 - Pool',
    ];
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityPersonCard(String name, String role, String phone) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            role,
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            phone,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
