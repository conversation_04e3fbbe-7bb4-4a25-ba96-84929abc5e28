import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../main/main_navigation_screen.dart';

class WaterManagementScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const WaterManagementScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<WaterManagementScreen> createState() => _WaterManagementScreenState();
}

class _WaterManagementScreenState extends ConsumerState<WaterManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Water',
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              // Navigate to home
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Colors.grey[100],
            child: TabBar(
              controller: _tabController,
              indicatorColor: Theme.of(context).primaryColor,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey[600],
              tabs: const [
                Tab(text: 'Overview'),
                Tab(text: 'Maintenance'),
                Tab(text: 'Contact Details'),
              ],
            ),
          ),

          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildMaintenanceTab(),
                _buildContactDetailsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(Icons.water_drop, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text(
                'Overview',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'General information about water for Jublee Hills Home',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Municipal Water Supply
          _buildSectionCard(
            title: 'Municipal Water Supply:',
            content: '4 water connections. 2 connections for House, 1 for Garden and other uses, 1 for Swimming pool.',
          ),

          const SizedBox(height: 16),

          // Borewell
          _buildSectionCard(
            title: 'Borewell:',
            content: '1 borewell, used only for non-residential purposes in case of shortfall.\n\nIf municipal water supply is disrupted, water tankers are used for residential purposes',
          ),

          const SizedBox(height: 16),

          // Water Distribution System
          _buildWaterDistributionCard(),

          const SizedBox(height: 16),

          // Overhead Water Tank Automation System
          _buildAutomationSystemCard(),
        ],
      ),
    );
  }

  Widget _buildSectionCard({required String title, required String content}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              content,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaterDistributionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Water Distribution System:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Two water pumps, one near the gate and one near the compound wall on the right side of the gate.\nTwo water tanks with a combined capacity of 5000 litres on the terrace. These tanks have two dedicated municipal water connection.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12),
            _buildBulletPoint('Tank 1 and Tank 2 are connected with a pipe in the center.'),
            _buildBulletPoint('Tank 1 has input from the Sump near the gate. This sump gets water from 2 separate municipal connections.'),
            _buildBulletPoint('Tank 1 serves exclusively for Ground Floor and 1st Floor.'),
            _buildBulletPoint('Tank 2 serves exclusively for 2nd Floor and it doesn\'t have any water input.'),
            _buildBulletPoint('Water tanks need 3 hours to fill when completely empty'),
            _buildBulletPoint('Of the remaining 2 municipal connections, one is used for Gardening and one is used for Swimming Pool.'),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 8),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutomationSystemCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'OVERHEAD WATER TANK AUTOMATION SYSTEM INSTALLED',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('System Provider:', 'Ktronics Technologies Solution'),
            _buildInfoRow('Technology:', 'Solar-powered, radio signal communication'),
            const SizedBox(height: 12),
            const Text(
              'Usecase:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildBulletPoint('One sensor device installed near the tank. It will have two sensors to check water levels to turn on and turn off the motor.'),
            _buildBulletPoint('The reciever device is installed near the Kitchen connected to the 1.5 HP Motor.'),
            const SizedBox(height: 12),
            const Text(
              'Trigger Mechanism',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildInfoRow('Auto Start:', 'When water level reaches 20%'),
            _buildInfoRow('Auto Stop:', 'When water level reaches 90%'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(Icons.build, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text(
                'Maintenance',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Maintenance information and schedule',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Maintenance Table
          _buildMaintenanceTable(),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTable() {
    final maintenanceItems = [
      {
        'item': 'Pumps & Motors',
        'status': 'No Issues',
        'frequency': 'Monthly checks',
        'lastChecked': '01.05.2025',
      },
      {
        'item': 'Tank Automation',
        'status': 'No Issues',
        'frequency': 'Weekly checks',
        'lastChecked': '12.05.2025',
      },
      {
        'item': 'Water Tank Cleaning',
        'status': 'No Issues',
        'frequency': 'Semi Annually',
        'lastChecked': '01.12.2024',
      },
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Table Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: const Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Item',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Frequency',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Last Checked',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Table Rows
            ...maintenanceItems.map((item) => _buildMaintenanceRow(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceRow(Map<String, String> item) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              item['item']!,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                item['status']!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[700],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            child: Text(
              item['frequency']!,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              item['lastChecked']!,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(Icons.contact_phone, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text(
                'Contact Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Emergency contacts and service providers',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // Contact cards will be added here
          _buildContactCard(
            'Ktronics Technologies',
            'Automation System Provider',
            '+91 98765 43210',
            '<EMAIL>',
          ),

          const SizedBox(height: 16),

          _buildContactCard(
            'Municipal Water Board',
            'Water Supply Authority',
            '1916',
            '<EMAIL>',
          ),

          const SizedBox(height: 16),

          _buildContactCard(
            'Emergency Plumber',
            '24/7 Plumbing Services',
            '+91 98765 12345',
            '<EMAIL>',
          ),
        ],
      ),
    );
  }

  Widget _buildContactCard(String name, String role, String phone, String email) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              role,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  phone,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.email, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    email,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Handle call action
                    },
                    icon: const Icon(Icons.phone, size: 16),
                    label: const Text('Call'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Handle email action
                    },
                    icon: const Icon(Icons.email, size: 16),
                    label: const Text('Email'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.grey[700],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
