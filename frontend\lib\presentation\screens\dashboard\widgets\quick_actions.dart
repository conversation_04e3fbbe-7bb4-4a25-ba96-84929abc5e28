import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Actions Grid
            LayoutBuilder(
              builder: (context, constraints) {
                final crossAxisCount = constraints.maxWidth > 800 ? 4 :
                                     constraints.maxWidth > 600 ? 3 : 2;
                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: 1.1,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                _buildActionItem(
                  context,
                  'Submit Issue',
                  Icons.report_problem,
                  AppTheme.warningColor,
                  () => _navigateToSubmitIssue(context),
                ),
                _buildActionItem(
                  context,
                  'View Alerts',
                  Icons.notifications_active,
                  AppTheme.errorColor,
                  () => _navigateToAlerts(context),
                ),
                _buildActionItem(
                  context,
                  'Maintenance',
                  Icons.build,
                  AppTheme.infoColor,
                  () => _navigateToMaintenance(context),
                ),
                _buildActionItem(
                  context,
                  'Properties',
                  Icons.business,
                  AppTheme.successColor,
                  () => _navigateToProperties(context),
                ),
                _buildActionItem(
                  context,
                  'Reports',
                  Icons.analytics,
                  Colors.purple,
                  () => _navigateToReports(context),
                ),
                _buildActionItem(
                  context,
                  'Users',
                  Icons.people,
                  Colors.orange,
                  () => _navigateToUsers(context),
                ),
                _buildActionItem(
                  context,
                  'Settings',
                  Icons.settings,
                  Colors.grey[600]!,
                  () => _navigateToSettings(context),
                ),
                _buildActionItem(
                  context,
                  'Help',
                  Icons.help_outline,
                  Colors.blue,
                  () => _showHelp(context),
                ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 24,
                color: color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSubmitIssue(BuildContext context) {
    // Navigate to maintenance screen with submit issue tab
    context.go('/maintenance');
  }

  void _navigateToAlerts(BuildContext context) {
    // TODO: Navigate to alerts screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Alerts screen coming soon')),
    );
  }

  void _navigateToMaintenance(BuildContext context) {
    context.go('/maintenance');
  }

  void _navigateToProperties(BuildContext context) {
    context.go('/properties');
  }

  void _navigateToReports(BuildContext context) {
    context.go('/reports');
  }

  void _navigateToUsers(BuildContext context) {
    context.go('/users');
  }

  void _navigateToSettings(BuildContext context) {
    // TODO: Navigate to settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings screen coming soon')),
    );
  }

  void _showHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Need help with the dashboard?'),
            SizedBox(height: 16),
            Text('• Use Quick Actions for common tasks'),
            Text('• Check System Status for health overview'),
            Text('• Monitor Properties for real-time updates'),
            Text('• Review Alerts for critical issues'),
            SizedBox(height: 16),
            Text('For technical support, contact your administrator.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
