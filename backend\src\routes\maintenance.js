const express = require('express');
const router = express.Router();

// Import controllers
const maintenanceController = require('../controllers/maintenanceController');
const attachmentController = require('../controllers/attachmentController');

// Import middleware
const auth = require('../middleware/auth');
const rbac = require('../middleware/rbac');
const {
  validateCreateMaintenanceIssue,
  validateUpdateMaintenanceIssue,
  validateGetMaintenanceIssues,
  validateGetMaintenanceIssue,
  validateDeleteMaintenanceIssue,
  validateAddComment,
  validateGetComments,
  validateGetStatistics,
  validateAttachmentParams,
  validateFileUpload
} = require('../middleware/maintenanceValidation');

// Apply authentication to all routes
router.use(auth);

// Maintenance Issues Routes

// GET /v1/maintenance - Get all maintenance issues with filtering and pagination
router.get('/',
  validateGetMaintenanceIssues,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.getMaintenanceIssues
);

// GET /v1/maintenance/statistics - Get maintenance statistics
router.get('/statistics',
  validateGetStatistics,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.getMaintenanceStatistics
);

// GET /v1/maintenance/:id - Get single maintenance issue with details
router.get('/:id',
  validateGetMaintenanceIssue,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.getMaintenanceIssue
);

// POST /v1/maintenance - Create new maintenance issue
router.post('/',
  validateCreateMaintenanceIssue,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.createMaintenanceIssue
);

// PUT /v1/maintenance/:id - Update maintenance issue
router.put('/:id',
  validateUpdateMaintenanceIssue,
  rbac(['ADMIN', 'MANAGER', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.updateMaintenanceIssue
);

// DELETE /v1/maintenance/:id - Delete maintenance issue
router.delete('/:id',
  validateDeleteMaintenanceIssue,
  rbac(['ADMIN', 'MANAGER']),
  maintenanceController.deleteMaintenanceIssue
);

// Comments Routes

// GET /v1/maintenance/:id/comments - Get comments for maintenance issue
router.get('/:id/comments',
  validateGetComments,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.getComments
);

// POST /v1/maintenance/:id/comments - Add comment to maintenance issue
router.post('/:id/comments',
  validateAddComment,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  maintenanceController.addComment
);

// Attachments Routes

// GET /v1/maintenance/:id/attachments - Get attachments for maintenance issue
router.get('/:id/attachments',
  validateAttachmentParams,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  attachmentController.getAttachments
);

// POST /v1/maintenance/:id/attachments - Upload attachments to maintenance issue
router.post('/:id/attachments',
  validateAttachmentParams,
  validateFileUpload,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  attachmentController.uploadAttachment
);

// DELETE /v1/maintenance/:id/attachments/:attachmentId - Delete attachment
router.delete('/:id/attachments/:attachmentId',
  validateAttachmentParams,
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  attachmentController.deleteAttachment
);

// GET /v1/maintenance/attachments/:attachmentId/download - Download attachment
router.get('/attachments/:attachmentId/download',
  rbac(['ADMIN', 'MANAGER', 'EMPLOYEE', 'MAINTENANCE_PERSONNEL']),
  attachmentController.downloadAttachment
);

module.exports = router;
