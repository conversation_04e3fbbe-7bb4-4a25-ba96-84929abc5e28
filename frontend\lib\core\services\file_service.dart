import 'dart:io';
import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';
import 'api_client.dart';

class FileService {
  static final FileService _instance = FileService._internal();
  factory FileService() => _instance;
  FileService._internal();

  final ApiClient _apiClient = ApiClient();
  final ImagePicker _imagePicker = ImagePicker();

  // Upload user avatar
  Future<ApiResponse<FileUploadResponse>> uploadAvatar(File imageFile) async {
    try {
      // Validate file size
      final fileSize = await imageFile.length();
      if (fileSize > ApiConstants.maxFileSize) {
        return ApiResponse<FileUploadResponse>(
          success: false,
          error: 'FILE_TOO_LARGE',
          message: 'File size exceeds ${ApiConstants.maxFileSize / (1024 * 1024)}MB limit',
          timestamp: DateTime.now().toIso8601String(),
        );
      }

      // Create form data
      final formData = FormData.fromMap({
        'avatar': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'avatar.${_getFileExtension(imageFile.path)}',
        ),
      });

      final response = await _apiClient.upload<FileUploadResponse>(
        ApiConstants.uploadAvatar,
        formData: formData,
        fromJson: (json) => FileUploadResponse.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<FileUploadResponse>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to upload avatar: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Upload property images
  Future<ApiResponse<List<FileUploadResponse>>> uploadPropertyImages({
    required String propertyId,
    required List<File> imageFiles,
    ProgressCallback? onProgress,
  }) async {
    try {
      // Validate files
      for (final file in imageFiles) {
        final fileSize = await file.length();
        if (fileSize > ApiConstants.maxFileSize) {
          return ApiResponse<List<FileUploadResponse>>(
            success: false,
            error: 'FILE_TOO_LARGE',
            message: 'One or more files exceed ${ApiConstants.maxFileSize / (1024 * 1024)}MB limit',
            timestamp: DateTime.now().toIso8601String(),
          );
        }
      }

      // Create form data
      final formData = FormData.fromMap({
        'propertyId': propertyId,
        'images': await Future.wait(
          imageFiles.map((file) => MultipartFile.fromFile(
            file.path,
            filename: 'property_${DateTime.now().millisecondsSinceEpoch}.${_getFileExtension(file.path)}',
          )),
        ),
      });

      final response = await _apiClient.upload<List<FileUploadResponse>>(
        ApiConstants.uploadPropertyImages,
        formData: formData,
        onSendProgress: onProgress,
        fromJson: (json) => (json as List)
            .map((item) => FileUploadResponse.fromJson(item as Map<String, dynamic>))
            .toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<FileUploadResponse>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to upload property images: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Upload documents
  Future<ApiResponse<List<FileUploadResponse>>> uploadDocuments({
    required List<File> documentFiles,
    String? category,
    String? propertyId,
    ProgressCallback? onProgress,
  }) async {
    try {
      // Validate files
      for (final file in documentFiles) {
        final fileSize = await file.length();
        if (fileSize > ApiConstants.maxFileSize) {
          return ApiResponse<List<FileUploadResponse>>(
            success: false,
            error: 'FILE_TOO_LARGE',
            message: 'One or more files exceed ${ApiConstants.maxFileSize / (1024 * 1024)}MB limit',
            timestamp: DateTime.now().toIso8601String(),
          );
        }
      }

      // Create form data
      final formDataMap = <String, dynamic>{
        'documents': await Future.wait(
          documentFiles.map((file) => MultipartFile.fromFile(
            file.path,
            filename: 'document_${DateTime.now().millisecondsSinceEpoch}.${_getFileExtension(file.path)}',
          )),
        ),
      };

      if (category != null) formDataMap['category'] = category;
      if (propertyId != null) formDataMap['propertyId'] = propertyId;

      final formData = FormData.fromMap(formDataMap);

      final response = await _apiClient.upload<List<FileUploadResponse>>(
        ApiConstants.uploadDocuments,
        formData: formData,
        onSendProgress: onProgress,
        fromJson: (json) => (json as List)
            .map((item) => FileUploadResponse.fromJson(item as Map<String, dynamic>))
            .toList(),
      );

      return response;
    } catch (e) {
      return ApiResponse<List<FileUploadResponse>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to upload documents: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Pick image from camera or gallery
  Future<File?> pickImage({
    ImageSource source = ImageSource.gallery,
    int? imageQuality,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: imageQuality ?? 85,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Pick multiple images
  Future<List<File>> pickMultipleImages({
    int? imageQuality,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final List<XFile> pickedFiles = await _imagePicker.pickMultiImage(
        imageQuality: imageQuality ?? 85,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      return pickedFiles.map((file) => File(file.path)).toList();
    } catch (e) {
      return [];
    }
  }

  // Pick files using file picker
  Future<List<File>> pickFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool allowMultiple = false,
  }) async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
      );

      if (result != null) {
        return result.paths
            .where((path) => path != null)
            .map((path) => File(path!))
            .toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // Get file URL for serving
  String getFileUrl(String category, String filename) {
    return ApiConstants.serveFile
        .replaceAll('{category}', category)
        .replaceAll('{filename}', filename);
  }

  // Get full file URL
  String getFullFileUrl(String category, String filename) {
    return '${ApiConstants.baseUrl}${getFileUrl(category, filename)}';
  }

  // Validate file type
  bool isValidFileType(String filePath, List<String> allowedTypes) {
    final extension = _getFileExtension(filePath).toLowerCase();
    final mimeType = _getMimeType(extension);
    return allowedTypes.contains(mimeType);
  }

  // Validate image file
  bool isValidImageFile(String filePath) {
    return isValidFileType(filePath, ApiConstants.allowedImageTypes);
  }

  // Validate document file
  bool isValidDocumentFile(String filePath) {
    return isValidFileType(filePath, ApiConstants.allowedDocumentTypes);
  }

  // Get file size in human readable format
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Get file extension
  String _getFileExtension(String filePath) {
    return filePath.split('.').last;
  }

  // Get MIME type from extension
  String _getMimeType(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }

  // Show image picker options
  Future<File?> showImagePickerOptions() async {
    // This would typically show a dialog to choose between camera and gallery
    // For now, we'll default to gallery
    return await pickImage(source: ImageSource.gallery);
  }

  // Compress image if needed
  Future<File?> compressImage(File imageFile, {int quality = 85}) async {
    try {
      // For now, we'll return the original file
      // In a real implementation, you might use image compression libraries
      return imageFile;
    } catch (e) {
      return null;
    }
  }
}

// File upload response model
class FileUploadResponse {
  final String filename;
  final String originalName;
  final String mimetype;
  final int size;
  final String url;
  final String uploadedAt;

  const FileUploadResponse({
    required this.filename,
    required this.originalName,
    required this.mimetype,
    required this.size,
    required this.url,
    required this.uploadedAt,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      filename: json['filename'] ?? '',
      originalName: json['originalName'] ?? '',
      mimetype: json['mimetype'] ?? '',
      size: json['size'] ?? 0,
      url: json['url'] ?? '',
      uploadedAt: json['uploadedAt'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'filename': filename,
      'originalName': originalName,
      'mimetype': mimetype,
      'size': size,
      'url': url,
      'uploadedAt': uploadedAt,
    };
  }
}
