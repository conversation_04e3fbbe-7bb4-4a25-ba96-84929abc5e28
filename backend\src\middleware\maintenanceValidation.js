const { body, param, query } = require('express-validator');
const mongoose = require('mongoose');

// Validation for creating maintenance issue
exports.validateCreateMaintenanceIssue = [
  body('propertyId')
    .notEmpty()
    .withMessage('Property ID is required')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid property ID');
      }
      return true;
    }),

  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters')
    .trim(),

  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters')
    .trim(),

  body('priority')
    .notEmpty()
    .withMessage('Priority is required')
    .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
    .withMessage('Priority must be one of: LOW, MEDIUM, HIGH, CRITICAL'),

  body('department')
    .notEmpty()
    .withMessage('Department is required')
    .isIn(['ELECTRICITY', 'WATER', 'SECURITY', 'INTERNET', 'GENERAL'])
    .withMessage('Department must be one of: ELECTRICITY, WATER, SECURITY, INTERNET, GENERAL'),

  body('assignedTo')
    .optional()
    .custom((value) => {
      if (value && !mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid assigned user ID');
      }
      return true;
    }),

  body('estimatedCost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Estimated cost must be a positive number'),

  body('recurrence')
    .optional()
    .isIn(['NONE', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'])
    .withMessage('Recurrence must be one of: NONE, DAILY, WEEKLY, MONTHLY, YEARLY')
];

// Validation for updating maintenance issue
exports.validateUpdateMaintenanceIssue = [
  param('id')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid issue ID');
      }
      return true;
    }),

  body('title')
    .optional()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters')
    .trim(),

  body('description')
    .optional()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters')
    .trim(),

  body('status')
    .optional()
    .isIn(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'])
    .withMessage('Status must be one of: OPEN, IN_PROGRESS, RESOLVED, CLOSED'),

  body('priority')
    .optional()
    .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
    .withMessage('Priority must be one of: LOW, MEDIUM, HIGH, CRITICAL'),

  body('assignedTo')
    .optional()
    .custom((value) => {
      if (value && !mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid assigned user ID');
      }
      return true;
    }),

  body('actualCost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Actual cost must be a positive number'),

  body('resolvedDate')
    .optional()
    .isISO8601()
    .withMessage('Resolved date must be a valid ISO 8601 date')
];

// Validation for getting maintenance issues
exports.validateGetMaintenanceIssues = [
  query('propertyId')
    .optional()
    .custom((value) => {
      if (value && !mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid property ID');
      }
      return true;
    }),

  query('status')
    .optional()
    .isIn(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'])
    .withMessage('Status must be one of: OPEN, IN_PROGRESS, RESOLVED, CLOSED'),

  query('priority')
    .optional()
    .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
    .withMessage('Priority must be one of: LOW, MEDIUM, HIGH, CRITICAL'),

  query('department')
    .optional()
    .isIn(['ELECTRICITY', 'WATER', 'SECURITY', 'INTERNET', 'GENERAL'])
    .withMessage('Department must be one of: ELECTRICITY, WATER, SECURITY, INTERNET, GENERAL'),

  query('assignedTo')
    .optional()
    .custom((value) => {
      if (value && !mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid assigned user ID');
      }
      return true;
    }),

  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters')
    .trim(),

  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('sortBy')
    .optional()
    .isIn(['reportedDate', 'priority', 'status', 'title', 'department'])
    .withMessage('Sort by must be one of: reportedDate, priority, status, title, department'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be either asc or desc')
];

// Validation for getting single maintenance issue
exports.validateGetMaintenanceIssue = [
  param('id')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid issue ID');
      }
      return true;
    })
];

// Validation for deleting maintenance issue
exports.validateDeleteMaintenanceIssue = [
  param('id')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid issue ID');
      }
      return true;
    })
];

// Validation for adding comment
exports.validateAddComment = [
  param('id')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid issue ID');
      }
      return true;
    }),

  body('comment')
    .notEmpty()
    .withMessage('Comment is required')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Comment must be between 1 and 1000 characters')
    .trim()
];

// Validation for getting comments
exports.validateGetComments = [
  param('id')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid issue ID');
      }
      return true;
    }),

  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
];

// Validation for getting statistics
exports.validateGetStatistics = [
  query('propertyId')
    .optional()
    .custom((value) => {
      if (value && !mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid property ID');
      }
      return true;
    }),

  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid ISO 8601 date'),

  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (value && req.query.dateFrom) {
        const dateFrom = new Date(req.query.dateFrom);
        const dateTo = new Date(value);
        if (dateTo <= dateFrom) {
          throw new Error('Date to must be after date from');
        }
      }
      return true;
    })
];

// Validation for attachment operations
exports.validateAttachmentParams = [
  param('id')
    .custom((value) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid issue ID');
      }
      return true;
    }),

  param('attachmentId')
    .optional()
    .custom((value) => {
      if (value && !mongoose.Types.ObjectId.isValid(value)) {
        throw new Error('Invalid attachment ID');
      }
      return true;
    })
];

// Custom validation for file uploads
exports.validateFileUpload = (req, res, next) => {
  // This will be handled by multer middleware in the controller
  // Additional custom validations can be added here if needed
  next();
};
