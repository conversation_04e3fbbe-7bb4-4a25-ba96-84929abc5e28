# 🧪 Dashboard V2 Testing Guide

## 🔐 Test Credentials

The backend has been seeded with test users. Use these credentials to test Dashboard V2:

### **Admin User (Full Access)**
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: SUPER_ADMIN
- **Access**: All features, all properties

### **Property Manager**
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: PROPERTY_MANAGER
- **Access**: Assigned properties, dashboard, reports

### **Office Manager**
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: OFFICE_MANAGER
- **Access**: Office management, dashboard

### **Security Personnel**
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: SECURITY_PERSONNEL
- **Access**: Security features, limited dashboard

## 🚀 Testing Steps

### **Step 1: Start Backend Server**
```bash
cd backend
npm run dev
```
Backend should be running at: `http://localhost:3000`

### **Step 2: Start Frontend App**
```bash
cd frontend
flutter run
```

### **Step 3: Test Authentication Flow**

1. **Open the app** - You'll see the splash screen
2. **Navigate to Login** - App will redirect to login screen
3. **Login with test credentials** - Use any of the credentials above
4. **Access Dashboard** - After login, you'll see Dashboard V1

### **Step 4: Test Dashboard V2 Toggle**

Now you should see **multiple ways** to access Dashboard V2:

#### **Option 1: App Bar Button**
- Look for blue **"V2"** button in the top-right corner
- Click to switch to Dashboard V2

#### **Option 2: Floating Action Button**
- Look for blue **"Try V2"** floating button in bottom-right
- Click to switch to Dashboard V2

#### **Option 3: Promotion Banner**
- Look for blue banner at top with **"New Dashboard V2 Available!"**
- Click **"Try V2"** button in the banner

### **Step 5: Explore Dashboard V2 Features**

Once in Dashboard V2, you should see:

#### **Enhanced UI Components**
- ✅ **Dashboard Header** - Key metrics with health indicators
- ✅ **Quick Actions** - 8 action buttons for fast navigation
- ✅ **System Status Card** - Real-time system health grid
- ✅ **Property Grid** - Interactive property cards
- ✅ **System Health Chart** - Visual pie chart
- ✅ **Performance Metrics** - Uptime, incidents, resolution rates
- ✅ **Alerts Feed** - Real-time alerts with severity indicators
- ✅ **Recent Activities** - Activity log with user actions

#### **Interactive Features**
- ✅ **Time Range Selector** - Click schedule icon (24h/7d/30d/90d)
- ✅ **Auto Refresh Toggle** - Click sync icon to enable/disable
- ✅ **Manual Refresh** - Pull down or click refresh button
- ✅ **Settings Panel** - Click settings icon for dashboard options

#### **Real Data Integration**
- ✅ **Live API Calls** - Data from `/v1/dashboard/overview`
- ✅ **Real Alerts** - Data from `/v1/dashboard/alerts`
- ✅ **Property Information** - Actual property data
- ✅ **System Health** - Real system status data

### **Step 6: Test Version Switching**

#### **From V2 back to V1**
- Click grey **"V1"** button in V2 app bar
- Should switch back to original dashboard

#### **Authentication Required Flow**
- If you're not logged in and try to access V2:
  - Banner shows **"Dashboard V2 - Login Required"**
  - Buttons show **"Login"** instead of **"Try V2"**
  - Clicking redirects to login screen

## 🔍 What to Look For

### **✅ Expected Behavior**

#### **When Not Logged In**
- Dashboard V1 shows static data
- V2 buttons show "Login" text
- Banner shows "Login Required" message
- Clicking V2 buttons redirects to login

#### **When Logged In**
- Dashboard V1 shows static data
- V2 buttons show "V2" or "Try V2" text
- Banner shows "New Dashboard V2 Available!"
- Clicking V2 buttons switches to Dashboard V2
- Dashboard V2 shows real data from API

#### **Dashboard V2 Features**
- Real-time data loading
- Interactive charts and widgets
- Time range filtering works
- Auto-refresh functionality
- Proper error handling
- Smooth navigation between sections

### **🚨 Potential Issues**

#### **Authentication Errors**
- If you see "Authentication required" in V2
- Make sure you're logged in with valid credentials
- Check that backend is running on correct port
- Verify API endpoints are accessible

#### **Network Errors**
- If you see "Failed to load dashboard"
- Check backend server is running
- Verify API base URL in frontend config
- Check browser network tab for failed requests

#### **UI Issues**
- If toggle buttons don't appear
- Try hot restart (not just hot reload)
- Check console for any error messages
- Verify all imports are correct

## 🎯 Test Scenarios

### **Scenario 1: Admin User**
1. Login as admin
2. Access Dashboard V2
3. Verify all data loads correctly
4. Test time range filtering
5. Test auto-refresh toggle
6. Check all quick actions work

### **Scenario 2: Property Manager**
1. Login as property manager
2. Access Dashboard V2
3. Verify only assigned properties show
4. Test property filtering
5. Check permission-based features

### **Scenario 3: Unauthenticated User**
1. Don't log in (or logout)
2. Try to access Dashboard V2
3. Verify proper redirect to login
4. Check error messages are helpful

### **Scenario 4: Version Switching**
1. Start with V1
2. Switch to V2
3. Switch back to V1
4. Verify state is preserved
5. Test multiple switches

## 📊 Success Criteria

### **✅ Dashboard V2 is successful if:**
- Authentication flow works correctly
- Real data loads from backend APIs
- All UI components render properly
- Interactive features work as expected
- Error handling is graceful
- Version switching is seamless
- Performance is acceptable
- Mobile responsiveness works

### **🎉 Bonus Points:**
- Auto-refresh works smoothly
- Charts are interactive and informative
- Quick actions navigate correctly
- Time filtering updates data properly
- Offline handling works gracefully

## 🛠️ Troubleshooting

### **Backend Issues**
```bash
# Check if backend is running
curl http://localhost:3000/health

# Check if database is seeded
curl http://localhost:3000/v1/auth/login -X POST \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### **Frontend Issues**
```bash
# Hot restart Flutter app
flutter run --hot

# Check for compilation errors
flutter analyze

# Clear cache if needed
flutter clean && flutter pub get
```

### **API Issues**
- Check browser Network tab for failed requests
- Verify authentication headers are being sent
- Check API response format matches expected models

---

## 🎉 Ready to Test!

Dashboard V2 is now fully implemented with proper authentication integration. The system will:

1. **Guide users to login** when not authenticated
2. **Provide multiple access points** to Dashboard V2
3. **Show real data** when authenticated
4. **Handle errors gracefully** with helpful messages
5. **Allow seamless switching** between V1 and V2

Happy testing! 🚀
