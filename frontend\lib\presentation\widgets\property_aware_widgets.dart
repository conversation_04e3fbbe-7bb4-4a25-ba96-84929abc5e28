import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/breadcrumb_permission_provider.dart';
import '../../core/services/auth_service.dart';

// Property assignment models
class PropertyAssignment {
  final String id;
  final String propertyId;
  final String propertyName;
  final String roleContext;
  final DateTime assignedAt;
  final DateTime? expiresAt;
  final bool isActive;

  const PropertyAssignment({
    required this.id,
    required this.propertyId,
    required this.propertyName,
    required this.roleContext,
    required this.assignedAt,
    this.expiresAt,
    required this.isActive,
  });

  factory PropertyAssignment.fromJson(Map<String, dynamic> json) {
    return PropertyAssignment(
      id: json['id'] ?? '',
      propertyId: json['propertyId'] ?? '',
      propertyName: json['propertyName'] ?? '',
      roleContext: json['roleContext'] ?? '',
      assignedAt: DateTime.parse(json['assignedAt']),
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      isActive: json['isActive'] ?? false,
    );
  }

  bool get isExpired => expiresAt != null && expiresAt!.isBefore(DateTime.now());
  bool get isValid => isActive && !isExpired;
}

// Property access service
class PropertyAccessService {
  final BreadcrumbPermissionService _permissionService = BreadcrumbPermissionService();

  Future<List<PropertyAssignment>> getUserAssignedProperties() async {
    try {
      final response = await _permissionService._apiClient.get<List<dynamic>>(
        '/auth/assigned-properties',
        fromJson: (json) => json as List<dynamic>,
      );

      if (response.success && response.data != null) {
        return response.data!
            .map((item) => PropertyAssignment.fromJson(item as Map<String, dynamic>))
            .where((assignment) => assignment.isValid)
            .toList();
      }
    } catch (e) {
      print('Error getting assigned properties: $e');
    }

    return [];
  }

  Future<bool> hasPropertyAccess(String propertyId) async {
    try {
      final response = await _permissionService._apiClient.get<Map<String, dynamic>>(
        '/auth/check-property-access',
        queryParameters: {'propertyId': propertyId},
        fromJson: (json) => json as Map<String, dynamic>,
      );

      return response.success && (response.data?['granted'] == true);
    } catch (e) {
      print('Error checking property access: $e');
      return false;
    }
  }
}

// Providers
final propertyAccessServiceProvider = Provider<PropertyAccessService>((ref) {
  return PropertyAccessService();
});

final assignedPropertiesProvider = FutureProvider<List<PropertyAssignment>>((ref) async {
  final service = ref.watch(propertyAccessServiceProvider);
  final authService = AuthService();
  
  if (!authService.isAuthenticated) {
    return [];
  }

  return await service.getUserAssignedProperties();
});

final propertyAccessProvider = FutureProvider.family<bool, String>((ref, propertyId) async {
  final service = ref.watch(propertyAccessServiceProvider);
  return await service.hasPropertyAccess(propertyId);
});

// Property-aware screen wrapper
class PropertyAwareScreen extends ConsumerWidget {
  final String propertyId;
  final Widget child;
  final Widget? unauthorizedWidget;

  const PropertyAwareScreen({
    super.key,
    required this.propertyId,
    required this.child,
    this.unauthorizedWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final accessAsync = ref.watch(propertyAccessProvider(propertyId));

    return accessAsync.when(
      data: (hasAccess) {
        if (hasAccess) {
          return child;
        } else {
          return unauthorizedWidget ?? _buildUnauthorizedScreen(context);
        }
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorScreen(context, error.toString()),
    );
  }

  Widget _buildUnauthorizedScreen(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Access Denied')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Property Access Denied',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You don\'t have access to this property.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'Please contact your administrator if you believe this is an error.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(BuildContext context, String error) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Error Checking Access',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Property selector dropdown
class PropertySelector extends ConsumerWidget {
  final String? selectedPropertyId;
  final Function(String?)? onPropertyChanged;
  final String? hintText;

  const PropertySelector({
    super.key,
    this.selectedPropertyId,
    this.onPropertyChanged,
    this.hintText,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final assignedPropertiesAsync = ref.watch(assignedPropertiesProvider);

    return assignedPropertiesAsync.when(
      data: (properties) {
        if (properties.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'No properties assigned',
              style: TextStyle(color: Colors.grey),
            ),
          );
        }

        return DropdownButtonFormField<String>(
          value: selectedPropertyId,
          hint: Text(hintText ?? 'Select Property'),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: properties.map((property) {
            return DropdownMenuItem<String>(
              value: property.propertyId,
              child: Row(
                children: [
                  const Icon(Icons.home, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          property.propertyName,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        if (property.roleContext.isNotEmpty)
                          Text(
                            property.roleContext,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (property.expiresAt != null)
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.orange.shade600,
                    ),
                ],
              ),
            );
          }).toList(),
          onChanged: onPropertyChanged,
        );
      },
      loading: () => const DropdownButtonFormField<String>(
        items: [],
        onChanged: null,
        decoration: InputDecoration(
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          suffixIcon: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      ),
      error: (error, stack) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.red.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Error loading properties: $error',
          style: const TextStyle(color: Colors.red),
        ),
      ),
    );
  }
}

// Property assignment card
class PropertyAssignmentCard extends StatelessWidget {
  final PropertyAssignment assignment;
  final VoidCallback? onTap;

  const PropertyAssignmentCard({
    super.key,
    required this.assignment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.home, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      assignment.propertyName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (assignment.expiresAt != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Expires ${_formatDate(assignment.expiresAt!)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade800,
                        ),
                      ),
                    ),
                ],
              ),
              if (assignment.roleContext.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'Role: ${assignment.roleContext}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Text(
                'Assigned: ${_formatDate(assignment.assignedAt)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Property assignment list
class PropertyAssignmentList extends ConsumerWidget {
  final Function(String)? onPropertySelected;

  const PropertyAssignmentList({
    super.key,
    this.onPropertySelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final assignedPropertiesAsync = ref.watch(assignedPropertiesProvider);

    return assignedPropertiesAsync.when(
      data: (properties) {
        if (properties.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.home_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No Properties Assigned',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Contact your administrator to get property access.',
                  style: TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: properties.length,
          itemBuilder: (context, index) {
            final property = properties[index];
            return PropertyAssignmentCard(
              assignment: property,
              onTap: () => onPropertySelected?.call(property.propertyId),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Error Loading Properties',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: const TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
