import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';
import '../../data/models/notification.dart';
import 'api_client.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get notifications with pagination and filtering
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getNotifications({
    int page = 1,
    int limit = ApiConstants.defaultPageSize,
    String? type,
    String? priority,
    bool? isRead,
    String? propertyId,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (type != null) queryParams['type'] = type;
      if (priority != null) queryParams['priority'] = priority;
      if (isRead != null) queryParams['isRead'] = isRead.toString();
      if (propertyId != null) queryParams['propertyId'] = propertyId;

      final response = await _apiClient.get<PaginatedResponse<NotificationModel>>(
        ApiConstants.notificationsList,
        queryParameters: queryParams,
        fromJson: (json) => PaginatedResponse.fromJson(
          json,
          (item) => NotificationModel.fromJson(item as Map<String, dynamic>),
        ),
      );

      return response;
    } catch (e) {
      return ApiResponse<PaginatedResponse<NotificationModel>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch notifications: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Create new notification (Admin only)
  Future<ApiResponse<NotificationModel>> createNotification({
    required String title,
    required String message,
    required String type,
    String? priority,
    List<String>? targetUsers,
    List<String>? targetRoles,
    String? propertyId,
    String? scheduledFor,
    String? expiresAt,
  }) async {
    try {
      final data = {
        'title': title,
        'message': message,
        'type': type,
        if (priority != null) 'priority': priority,
        if (targetUsers != null) 'targetUsers': targetUsers,
        if (targetRoles != null) 'targetRoles': targetRoles,
        if (propertyId != null) 'propertyId': propertyId,
        if (scheduledFor != null) 'scheduledFor': scheduledFor,
        if (expiresAt != null) 'expiresAt': expiresAt,
      };

      final response = await _apiClient.post<NotificationModel>(
        ApiConstants.notificationsList,
        data: data,
        fromJson: (json) => NotificationModel.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<NotificationModel>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to create notification: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Mark notification as read
  Future<ApiResponse<void>> markAsRead(String notificationId) async {
    try {
      final path = ApiConstants.notificationRead.replaceAll('{notificationId}', notificationId);

      final response = await _apiClient.post<void>(path);

      return response;
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to mark notification as read: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Mark all notifications as read
  Future<ApiResponse<MarkAllReadResponse>> markAllAsRead() async {
    try {
      final response = await _apiClient.post<MarkAllReadResponse>(
        ApiConstants.notificationMarkAllRead,
        fromJson: (json) => MarkAllReadResponse.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<MarkAllReadResponse>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to mark all notifications as read: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get unread notification count
  Future<ApiResponse<UnreadCountResponse>> getUnreadCount() async {
    try {
      final response = await _apiClient.get<UnreadCountResponse>(
        ApiConstants.notificationUnreadCount,
        fromJson: (json) => UnreadCountResponse.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<UnreadCountResponse>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to get unread count: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get notification types for dropdown
  List<String> getNotificationTypes() {
    return ApiConstants.notificationTypes;
  }

  // Get notification priorities for dropdown
  List<String> getNotificationPriorities() {
    return ApiConstants.notificationPriorities;
  }

  // Helper method to get notification type display info
  Map<String, dynamic> getNotificationTypeInfo(String type) {
    switch (type) {
      case 'INFO':
        return {
          'name': 'Info',
          'color': 0xFF2196F3, // Blue
          'icon': 'info',
        };
      case 'WARNING':
        return {
          'name': 'Warning',
          'color': 0xFFFF9800, // Orange
          'icon': 'warning',
        };
      case 'ERROR':
        return {
          'name': 'Error',
          'color': 0xFFF44336, // Red
          'icon': 'error',
        };
      case 'SUCCESS':
        return {
          'name': 'Success',
          'color': 0xFF4CAF50, // Green
          'icon': 'check_circle',
        };
      default:
        return {
          'name': type,
          'color': 0xFF9E9E9E,
          'icon': 'notifications',
        };
    }
  }

  // Helper method to get notification priority display info
  Map<String, dynamic> getNotificationPriorityInfo(String priority) {
    switch (priority) {
      case 'LOW':
        return {
          'name': 'Low',
          'color': 0xFF4CAF50, // Green
          'icon': 'keyboard_arrow_down',
        };
      case 'MEDIUM':
        return {
          'name': 'Medium',
          'color': 0xFF2196F3, // Blue
          'icon': 'remove',
        };
      case 'HIGH':
        return {
          'name': 'High',
          'color': 0xFFFF9800, // Orange
          'icon': 'keyboard_arrow_up',
        };
      case 'URGENT':
        return {
          'name': 'Urgent',
          'color': 0xFFF44336, // Red
          'icon': 'priority_high',
        };
      default:
        return {
          'name': priority,
          'color': 0xFF9E9E9E,
          'icon': 'help',
        };
    }
  }

  // Format notification time for display
  String formatNotificationTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  // Group notifications by date
  Map<String, List<NotificationModel>> groupNotificationsByDate(List<NotificationModel> notifications) {
    final grouped = <String, List<NotificationModel>>{};
    
    for (final notification in notifications) {
      final date = notification.createdDateTime;
      final dateKey = _getDateKey(date);
      
      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }
    
    return grouped;
  }

  String _getDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate = DateTime(date.year, date.month, date.day);
    
    if (notificationDate == today) {
      return 'Today';
    } else if (notificationDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(notificationDate).inDays < 7) {
      return '${_getDayName(notificationDate.weekday)}';
    } else {
      return '${notificationDate.day}/${notificationDate.month}/${notificationDate.year}';
    }
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Monday';
      case 2: return 'Tuesday';
      case 3: return 'Wednesday';
      case 4: return 'Thursday';
      case 5: return 'Friday';
      case 6: return 'Saturday';
      case 7: return 'Sunday';
      default: return 'Unknown';
    }
  }

  // Filter notifications by criteria
  List<NotificationModel> filterNotifications(
    List<NotificationModel> notifications, {
    String? type,
    String? priority,
    bool? isRead,
    bool? isActive,
  }) {
    return notifications.where((notification) {
      if (type != null && notification.type != type) return false;
      if (priority != null && notification.priority != priority) return false;
      if (isRead != null && notification.isRead != isRead) return false;
      if (isActive != null && notification.isActive != isActive) return false;
      return true;
    }).toList();
  }

  // Sort notifications by priority and date
  List<NotificationModel> sortNotifications(List<NotificationModel> notifications) {
    return notifications..sort((a, b) {
      // First sort by read status (unread first)
      if (a.isRead != b.isRead) {
        return a.isRead ? 1 : -1;
      }
      
      // Then by priority
      final priorityOrder = {'URGENT': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3};
      final aPriority = priorityOrder[a.priority] ?? 4;
      final bPriority = priorityOrder[b.priority] ?? 4;
      
      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }
      
      // Finally by date (newest first)
      return b.createdDateTime.compareTo(a.createdDateTime);
    });
  }
}
