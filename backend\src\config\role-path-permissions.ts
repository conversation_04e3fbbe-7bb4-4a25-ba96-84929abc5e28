// Role-Based Breadcrumb Path Permissions

export interface PathPermissionRule {
  path: string;
  accessLevel: 'full' | 'read' | 'write' | 'restricted' | 'none';
  permissions: string[]; // Specific permissions granted
  restrictions?: {
    hideComponents?: string[]; // Component IDs to hide
    disableComponents?: string[]; // Component IDs to disable
    hideFields?: string[]; // Specific fields to hide
    customConditions?: Record<string, any>; // Custom access conditions
  };
  conditions?: Record<string, any>; // ABAC conditions
}

export interface RolePathPermissions {
  role: string;
  permissions: PathPermissionRule[];
  inheritance?: {
    inheritsFrom?: string; // Parent role to inherit from
    overrides?: string[]; // Paths that override inheritance
  };
}

export const ROLE_PATH_PERMISSIONS: RolePathPermissions[] = [
  // SUPER_ADMIN - Full access to everything
  {
    role: 'SUPER_ADMIN',
    permissions: [
      { path: '/**', accessLevel: 'full', permissions: ['*'] }, // Wildcard for all paths
    ]
  },

  // PROPERTY_MANAGER - Full property management access
  {
    role: 'PROPERTY_MANAGER',
    permissions: [
      { path: '/dashboard', accessLevel: 'full', permissions: ['view', 'view_financial'] },
      { path: '/properties', accessLevel: 'full', permissions: ['view', 'create', 'update', 'delete'] },
      { path: '/properties/{propertyId}', accessLevel: 'full', permissions: ['view', 'update', 'view_financial'] },
      { path: '/properties/{propertyId}/systems', accessLevel: 'full', permissions: ['view'] },
      { path: '/properties/{propertyId}/systems/**', accessLevel: 'full', permissions: ['*'] },
      { path: '/maintenance', accessLevel: 'full', permissions: ['view', 'create', 'update', 'assign', 'view_costs'] },
      { path: '/maintenance/**', accessLevel: 'full', permissions: ['*'] },
      { path: '/office', accessLevel: 'read', permissions: ['view'] },
      { path: '/office/**', accessLevel: 'read', permissions: ['view', 'view_reports'] },
    ]
  },

  // SECURITY_PERSONNEL - Limited security system access
  {
    role: 'SECURITY_PERSONNEL',
    permissions: [
      { 
        path: '/dashboard', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['dashboard.financial_overview'],
        }
      },
      { 
        path: '/properties', 
        accessLevel: 'read', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['financial_data', 'cost_info'],
        }
      },
      { 
        path: '/properties/{propertyId}', 
        accessLevel: 'read', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['property.financial_summary'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems', 
        accessLevel: 'read', 
        permissions: ['view'] 
      },
      { 
        path: '/properties/{propertyId}/systems/security', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['installation_cost', 'vendor_contracts'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/cctv', 
        accessLevel: 'restricted', 
        permissions: ['view', 'view_live'],
        restrictions: {
          hideComponents: ['cctv.add_camera', 'cctv.settings'],
          disableComponents: ['cctv.camera_controls'],
          customConditions: {
            recordingRetentionDays: 7,
            canViewLiveFeeds: true,
            canControlPTZ: false,
          }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/cctv/camera/{cameraId}', 
        accessLevel: 'restricted', 
        permissions: ['view_live'],
        restrictions: {
          hideComponents: ['camera.settings'],
          disableComponents: ['camera.controls'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/access-control', 
        accessLevel: 'restricted', 
        permissions: ['view', 'view_logs', 'emergency_override'],
        restrictions: {
          hideComponents: ['access.user_management', 'access.add_user'],
          customConditions: {
            emergencyOverrideOnly: true,
            logRetentionDays: 1, // Only current day logs
          }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/access-control/users', 
        accessLevel: 'none', 
        permissions: [] 
      },
      { 
        path: '/properties/{propertyId}/systems/security/maintenance', 
        accessLevel: 'read', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['maintenance_cost', 'vendor_details'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems/electricity', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['cost_analysis', 'vendor_contracts'],
          customConditions: { emergencyInfoOnly: true }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/electricity/generator', 
        accessLevel: 'restricted', 
        permissions: ['view', 'control'],
        restrictions: {
          customConditions: {
            emergencyUseOnly: true,
            canViewFuelLevel: true,
            canStartGenerator: true,
          }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/electricity/ups', 
        accessLevel: 'read', 
        permissions: ['view'] 
      },
      { path: '/properties/{propertyId}/systems/water', accessLevel: 'none', permissions: [] },
      { path: '/properties/{propertyId}/systems/ott', accessLevel: 'none', permissions: [] },
      { path: '/office', accessLevel: 'none', permissions: [] },
      { path: '/maintenance', accessLevel: 'none', permissions: [] },
    ]
  },

  // MAINTENANCE_STAFF - Maintenance-focused access
  {
    role: 'MAINTENANCE_STAFF',
    permissions: [
      { 
        path: '/dashboard', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['dashboard.financial_overview'],
        }
      },
      { 
        path: '/properties', 
        accessLevel: 'read', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['financial_data', 'strategic_info'],
        }
      },
      { 
        path: '/properties/{propertyId}', 
        accessLevel: 'read', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['property.financial_summary'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems', 
        accessLevel: 'read', 
        permissions: ['view'] 
      },
      { 
        path: '/properties/{propertyId}/systems/security', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['live_feeds', 'recordings', 'access_logs'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/cctv', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['cctv.live_feeds', 'cctv.recordings', 'cctv.camera_controls'],
          customConditions: { maintenanceTasksOnly: true }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/access-control', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['access.user_management', 'access.access_logs'],
          customConditions: { maintenanceTasksOnly: true }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/security/maintenance', 
        accessLevel: 'full', 
        permissions: ['view', 'create', 'update', 'assign'] 
      },
      { 
        path: '/properties/{propertyId}/systems/electricity', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['cost_analysis', 'vendor_contracts'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems/electricity/generator', 
        accessLevel: 'restricted', 
        permissions: ['view', 'update_fuel', 'control'],
        restrictions: {
          hideFields: ['purchase_cost', 'warranty_details'],
          customConditions: {
            canUpdateFuelLevel: true,
            canRunTests: true,
            canViewMaintenanceSchedule: true,
          }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/electricity/ups', 
        accessLevel: 'restricted', 
        permissions: ['view', 'diagnostics'],
        restrictions: {
          customConditions: {
            canCheckBatteryStatus: true,
            canRunDiagnostics: true,
            canReplaceBatteries: true,
          }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/water', 
        accessLevel: 'restricted', 
        permissions: ['view', 'control'],
        restrictions: {
          hideFields: ['cost_analysis'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems/water/tanks', 
        accessLevel: 'restricted', 
        permissions: ['view', 'view_quality'],
        restrictions: {
          customConditions: {
            canViewLevels: true,
            canCleanTanks: true,
            canReportIssues: true,
          }
        }
      },
      { 
        path: '/properties/{propertyId}/systems/water/pumps', 
        accessLevel: 'restricted', 
        permissions: ['view', 'control', 'view_maintenance'],
        restrictions: {
          customConditions: {
            canOperatePumps: true,
            canPerformMaintenance: true,
          }
        }
      },
      { path: '/properties/{propertyId}/systems/ott', accessLevel: 'none', permissions: [] },
      { path: '/office', accessLevel: 'none', permissions: [] },
      { 
        path: '/maintenance', 
        accessLevel: 'full', 
        permissions: ['view', 'create', 'update', 'assign', 'view_costs'] 
      },
      { 
        path: '/maintenance/**', 
        accessLevel: 'full', 
        permissions: ['*'] 
      },
    ]
  },

  // OFFICE_MANAGER - Office operations only
  {
    role: 'OFFICE_MANAGER',
    permissions: [
      { 
        path: '/dashboard', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['dashboard.property_summary', 'dashboard.financial_overview'],
        }
      },
      { path: '/properties', accessLevel: 'none', permissions: [] },
      { 
        path: '/office', 
        accessLevel: 'full', 
        permissions: ['view', 'create', 'update'] 
      },
      { 
        path: '/office/attendance', 
        accessLevel: 'full', 
        permissions: ['view', 'mark', 'view_reports'] 
      },
      { 
        path: '/office/employees', 
        accessLevel: 'full', 
        permissions: ['view', 'create', 'view_details', 'view_salary'] 
      },
      { 
        path: '/maintenance', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideFields: ['cost_tracking', 'vendor_details'],
          customConditions: { officeMaintenanceOnly: true }
        }
      },
    ]
  },

  // CONSTRUCTION_SUPERVISOR - Construction site access
  {
    role: 'CONSTRUCTION_SUPERVISOR',
    permissions: [
      { 
        path: '/dashboard', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        restrictions: {
          hideComponents: ['dashboard.financial_overview'],
        }
      },
      { 
        path: '/properties', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        conditions: { propertyType: 'construction' }, // Only construction properties
        restrictions: {
          hideFields: ['financial_data'],
        }
      },
      { 
        path: '/properties/{propertyId}', 
        accessLevel: 'restricted', 
        permissions: ['view', 'update'],
        conditions: { propertyType: 'construction' },
        restrictions: {
          hideComponents: ['property.financial_summary'],
        }
      },
      { 
        path: '/properties/{propertyId}/systems', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        conditions: { propertyType: 'construction' }
      },
      { 
        path: '/properties/{propertyId}/systems/security', 
        accessLevel: 'restricted', 
        permissions: ['view'],
        conditions: { propertyType: 'construction' },
        restrictions: {
          customConditions: { constructionSiteOnly: true }
        }
      },
      { 
        path: '/maintenance', 
        accessLevel: 'full', 
        permissions: ['view', 'create', 'update', 'assign'],
        conditions: { propertyType: 'construction' }
      },
      { path: '/office', accessLevel: 'none', permissions: [] },
    ]
  },
];

// Helper function to resolve wildcard paths
export function resolvePathPermissions(role: string, requestedPath: string): PathPermissionRule | null {
  const rolePermissions = ROLE_PATH_PERMISSIONS.find(rp => rp.role === role);
  if (!rolePermissions) return null;

  // Find exact match first
  let matchedRule = rolePermissions.permissions.find(p => p.path === requestedPath);
  if (matchedRule) return matchedRule;

  // Find wildcard matches
  for (const permission of rolePermissions.permissions) {
    if (permission.path.includes('**')) {
      const basePath = permission.path.replace('/**', '');
      if (requestedPath.startsWith(basePath)) {
        matchedRule = permission;
        break;
      }
    } else if (permission.path.includes('{') && permission.path.includes('}')) {
      // Handle parameterized paths
      const pathPattern = permission.path.replace(/\{[^}]+\}/g, '[^/]+');
      const regex = new RegExp(`^${pathPattern}$`);
      if (regex.test(requestedPath)) {
        matchedRule = permission;
        break;
      }
    }
  }

  return matchedRule || null;
}
