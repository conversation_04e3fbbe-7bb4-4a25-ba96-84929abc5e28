// Role-Based Tab Permissions Configuration

export interface RoleTabPermission {
  tabId: string;
  accessLevel: 'full' | 'read' | 'restricted' | 'none';
  isVisible: boolean;
  isEnabled: boolean;
  restrictions?: {
    hideComponents?: string[];
    disableComponents?: string[];
    hideFields?: string[];
    customConditions?: Record<string, any>;
  };
}

export interface ScreenTabPermissions {
  screenPath: string;
  role: string;
  tabs: RoleTabPermission[];
}

export const ROLE_TAB_PERMISSIONS: ScreenTabPermissions[] = [
  // Security Management Screen Permissions
  {
    screenPath: '/properties/{propertyId}/systems/security',
    role: 'SUPER_ADMIN',
    tabs: [
      { tabId: 'overview', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'cctv', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'access_control', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'maintenance', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'contacts', accessLevel: 'full', isVisible: true, isEnabled: true },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/security',
    role: 'PROPERTY_MANAGER',
    tabs: [
      { tabId: 'overview', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'cctv', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'access_control', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'maintenance', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'contacts', accessLevel: 'full', isVisible: true, isEnabled: true },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/security',
    role: 'SECURITY_PERSONNEL',
    tabs: [
      {
        tabId: 'overview',
        accessLevel: 'read',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.overview.financial_summary'],
          hideFields: ['installation_cost', 'vendor_contracts'],
        },
      },
      {
        tabId: 'cctv',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.cctv.add_camera', 'security.cctv.settings'],
          disableComponents: ['security.cctv.camera_controls'],
          customConditions: {
            recordingRetentionDays: 7,
            canViewLiveFeeds: true,
            canControlPTZ: false,
          },
        },
      },
      {
        tabId: 'access_control',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.access.user_management', 'security.access.add_user'],
          customConditions: {
            emergencyOverrideOnly: true,
            logRetentionDays: 1,
          },
        },
      },
      {
        tabId: 'maintenance',
        accessLevel: 'read',
        isVisible: true,
        isEnabled: false,
        restrictions: {
          hideFields: ['maintenance_cost', 'vendor_details'],
        },
      },
      {
        tabId: 'contacts',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.contacts.contracts'],
          customConditions: {
            emergencyContactsOnly: true,
          },
        },
      },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/security',
    role: 'MAINTENANCE_STAFF',
    tabs: [
      {
        tabId: 'overview',
        accessLevel: 'read',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.overview.financial_summary'],
        },
      },
      {
        tabId: 'cctv',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.cctv.live_feeds', 'security.cctv.recordings', 'security.cctv.camera_controls'],
          customConditions: {
            maintenanceTasksOnly: true,
          },
        },
      },
      {
        tabId: 'access_control',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.access.user_management', 'security.access.access_logs'],
          customConditions: {
            maintenanceTasksOnly: true,
          },
        },
      },
      {
        tabId: 'maintenance',
        accessLevel: 'full',
        isVisible: true,
        isEnabled: true,
      },
      {
        tabId: 'contacts',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['security.contacts.contracts'],
          customConditions: {
            vendorContactsOnly: true,
          },
        },
      },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/security',
    role: 'OFFICE_MANAGER',
    tabs: [
      { tabId: 'overview', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'cctv', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'access_control', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'maintenance', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'contacts', accessLevel: 'none', isVisible: false, isEnabled: false },
    ],
  },

  // Electricity Management Screen Permissions
  {
    screenPath: '/properties/{propertyId}/systems/electricity',
    role: 'SUPER_ADMIN',
    tabs: [
      { tabId: 'overview', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'generator', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'ups', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'maintenance', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'contacts', accessLevel: 'full', isVisible: true, isEnabled: true },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/electricity',
    role: 'PROPERTY_MANAGER',
    tabs: [
      { tabId: 'overview', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'generator', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'ups', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'maintenance', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'contacts', accessLevel: 'full', isVisible: true, isEnabled: true },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/electricity',
    role: 'MAINTENANCE_STAFF',
    tabs: [
      {
        tabId: 'overview',
        accessLevel: 'read',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['electricity.overview.cost_analysis'],
        },
      },
      {
        tabId: 'generator',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideFields: ['purchase_cost', 'warranty_details'],
          customConditions: {
            canUpdateFuelLevel: true,
            canRunTests: true,
            canViewMaintenanceSchedule: true,
          },
        },
      },
      {
        tabId: 'ups',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          customConditions: {
            canCheckBatteryStatus: true,
            canRunDiagnostics: true,
            canReplaceBatteries: true,
          },
        },
      },
      {
        tabId: 'maintenance',
        accessLevel: 'full',
        isVisible: true,
        isEnabled: true,
      },
      {
        tabId: 'contacts',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          customConditions: {
            vendorContactsOnly: true,
          },
        },
      },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/electricity',
    role: 'SECURITY_PERSONNEL',
    tabs: [
      {
        tabId: 'overview',
        accessLevel: 'read',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          hideComponents: ['electricity.overview.cost_analysis'],
          customConditions: {
            emergencyInfoOnly: true,
          },
        },
      },
      {
        tabId: 'generator',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          customConditions: {
            canViewFuelLevel: true,
            canStartGenerator: true,
            emergencyUseOnly: true,
          },
        },
      },
      {
        tabId: 'ups',
        accessLevel: 'read',
        isVisible: true,
        isEnabled: false,
      },
      {
        tabId: 'maintenance',
        accessLevel: 'none',
        isVisible: false,
        isEnabled: false,
      },
      {
        tabId: 'contacts',
        accessLevel: 'restricted',
        isVisible: true,
        isEnabled: true,
        restrictions: {
          customConditions: {
            emergencyContactsOnly: true,
          },
        },
      },
    ],
  },
  {
    screenPath: '/properties/{propertyId}/systems/electricity',
    role: 'OFFICE_MANAGER',
    tabs: [
      { tabId: 'overview', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'generator', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'ups', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'maintenance', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'contacts', accessLevel: 'none', isVisible: false, isEnabled: false },
    ],
  },

  // Office Management Screen Permissions
  {
    screenPath: '/office',
    role: 'SUPER_ADMIN',
    tabs: [
      { tabId: 'overview', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'attendance', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'employees', accessLevel: 'full', isVisible: true, isEnabled: true },
    ],
  },
  {
    screenPath: '/office',
    role: 'OFFICE_MANAGER',
    tabs: [
      { tabId: 'overview', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'attendance', accessLevel: 'full', isVisible: true, isEnabled: true },
      { tabId: 'employees', accessLevel: 'full', isVisible: true, isEnabled: true },
    ],
  },
  {
    screenPath: '/office',
    role: 'PROPERTY_MANAGER',
    tabs: [
      { tabId: 'overview', accessLevel: 'read', isVisible: true, isEnabled: false },
      { tabId: 'attendance', accessLevel: 'read', isVisible: true, isEnabled: false },
      { tabId: 'employees', accessLevel: 'read', isVisible: true, isEnabled: false },
    ],
  },
  {
    screenPath: '/office',
    role: 'SECURITY_PERSONNEL',
    tabs: [
      { tabId: 'overview', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'attendance', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'employees', accessLevel: 'none', isVisible: false, isEnabled: false },
    ],
  },
  {
    screenPath: '/office',
    role: 'MAINTENANCE_STAFF',
    tabs: [
      { tabId: 'overview', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'attendance', accessLevel: 'none', isVisible: false, isEnabled: false },
      { tabId: 'employees', accessLevel: 'none', isVisible: false, isEnabled: false },
    ],
  },
];

// Helper functions
export function getRoleTabPermissions(screenPath: string, role: string): ScreenTabPermissions | undefined {
  return ROLE_TAB_PERMISSIONS.find(
    permission => permission.screenPath === screenPath && permission.role === role
  );
}

export function getTabPermission(screenPath: string, role: string, tabId: string): RoleTabPermission | undefined {
  const screenPermissions = getRoleTabPermissions(screenPath, role);
  return screenPermissions?.tabs.find(tab => tab.tabId === tabId);
}

export function isTabVisible(screenPath: string, role: string, tabId: string): boolean {
  const tabPermission = getTabPermission(screenPath, role, tabId);
  return tabPermission?.isVisible ?? false;
}

export function isTabEnabled(screenPath: string, role: string, tabId: string): boolean {
  const tabPermission = getTabPermission(screenPath, role, tabId);
  return tabPermission?.isEnabled ?? false;
}

export function getVisibleTabs(screenPath: string, role: string): string[] {
  const screenPermissions = getRoleTabPermissions(screenPath, role);
  return screenPermissions?.tabs
    .filter(tab => tab.isVisible)
    .map(tab => tab.tabId) ?? [];
}
