import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

class CacheManager {
  static const String _cacheDirectory = 'app_cache';
  static const Duration _defaultCacheDuration = Duration(hours: 1);
  
  late Directory _cacheDir;
  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;
    
    final appDir = await getApplicationDocumentsDirectory();
    _cacheDir = Directory('${appDir.path}/$_cacheDirectory');
    
    if (!await _cacheDir.exists()) {
      await _cacheDir.create(recursive: true);
    }
    
    _initialized = true;
  }

  /// Set cache data with optional duration
  Future<void> set<T>(
    String key, 
    T data, {
    Duration? duration,
  }) async {
    await _ensureInitialized();
    
    final cacheItem = CacheItem<T>(
      data: data,
      timestamp: DateTime.now(),
      duration: duration ?? _defaultCacheDuration,
    );
    
    final file = await _getCacheFile(key);
    final jsonData = jsonEncode(cacheItem.toJson());
    await file.writeAsString(jsonData);
  }

  /// Get cache data if not expired
  Future<T?> get<T>(String key) async {
    await _ensureInitialized();
    
    try {
      final file = await _getCacheFile(key);
      
      if (!await file.exists()) {
        return null;
      }
      
      final jsonData = await file.readAsString();
      final Map<String, dynamic> json = jsonDecode(jsonData);
      final cacheItem = CacheItem<T>.fromJson(json);
      
      // Check if cache is expired
      if (cacheItem.isExpired) {
        await file.delete();
        return null;
      }
      
      return cacheItem.data;
    } catch (e) {
      // If there's any error reading cache, return null
      return null;
    }
  }

  /// Check if cache exists and is not expired
  Future<bool> has(String key) async {
    await _ensureInitialized();
    
    try {
      final file = await _getCacheFile(key);
      
      if (!await file.exists()) {
        return false;
      }
      
      final jsonData = await file.readAsString();
      final Map<String, dynamic> json = jsonDecode(jsonData);
      final cacheItem = CacheItem.fromJson(json);
      
      if (cacheItem.isExpired) {
        await file.delete();
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove specific cache entry
  Future<void> remove(String key) async {
    await _ensureInitialized();
    
    final file = await _getCacheFile(key);
    if (await file.exists()) {
      await file.delete();
    }
  }

  /// Clear all cache
  Future<void> clear() async {
    await _ensureInitialized();
    
    if (await _cacheDir.exists()) {
      await _cacheDir.delete(recursive: true);
      await _cacheDir.create(recursive: true);
    }
  }

  /// Clear expired cache entries
  Future<void> clearExpired() async {
    await _ensureInitialized();
    
    final files = await _cacheDir.list().toList();
    
    for (final file in files) {
      if (file is File) {
        try {
          final jsonData = await file.readAsString();
          final Map<String, dynamic> json = jsonDecode(jsonData);
          final cacheItem = CacheItem.fromJson(json);
          
          if (cacheItem.isExpired) {
            await file.delete();
          }
        } catch (e) {
          // If we can't read the file, delete it
          await file.delete();
        }
      }
    }
  }

  /// Get cache size in bytes
  Future<int> getCacheSize() async {
    await _ensureInitialized();
    
    int totalSize = 0;
    final files = await _cacheDir.list().toList();
    
    for (final file in files) {
      if (file is File) {
        final stat = await file.stat();
        totalSize += stat.size;
      }
    }
    
    return totalSize;
  }

  /// Get formatted cache size
  Future<String> getFormattedCacheSize() async {
    final size = await getCacheSize();
    return _formatBytes(size);
  }

  /// Set cache with TTL (Time To Live) in seconds
  Future<void> setWithTTL<T>(String key, T data, int ttlSeconds) async {
    await set(key, data, duration: Duration(seconds: ttlSeconds));
  }

  /// Get cache file for a key
  Future<File> _getCacheFile(String key) async {
    final hashedKey = _hashKey(key);
    return File('${_cacheDir.path}/$hashedKey.cache');
  }

  /// Hash the cache key to avoid file system issues
  String _hashKey(String key) {
    final bytes = utf8.encode(key);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Ensure cache manager is initialized
  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

class CacheItem<T> {
  final T data;
  final DateTime timestamp;
  final Duration duration;

  CacheItem({
    required this.data,
    required this.timestamp,
    required this.duration,
  });

  bool get isExpired => DateTime.now().isAfter(timestamp.add(duration));

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration.inMilliseconds,
    };
  }

  factory CacheItem.fromJson(Map<String, dynamic> json) {
    return CacheItem<T>(
      data: json['data'] as T,
      timestamp: DateTime.parse(json['timestamp']),
      duration: Duration(milliseconds: json['duration']),
    );
  }
}

/// Cache configuration for different data types
class CacheConfig {
  static const Duration shortTerm = Duration(minutes: 5);
  static const Duration mediumTerm = Duration(minutes: 30);
  static const Duration longTerm = Duration(hours: 2);
  static const Duration veryLongTerm = Duration(hours: 24);

  // Specific cache durations for different data types
  static const Duration dashboardData = Duration(minutes: 5);
  static const Duration propertyData = Duration(minutes: 15);
  static const Duration maintenanceData = Duration(minutes: 5);
  static const Duration systemStatus = Duration(minutes: 2);
  static const Duration userProfile = Duration(hours: 1);
  static const Duration staticData = Duration(hours: 24);
}

/// Cache keys for consistent naming
class CacheKeys {
  static const String dashboardOverview = 'dashboard_overview';
  static const String properties = 'properties';
  static const String maintenanceIssues = 'maintenance_issues';
  static const String systemStatus = 'system_status';
  static const String userProfile = 'user_profile';
  static const String officeData = 'office_data';
  
  static String propertyDetail(String propertyId) => 'property_detail_$propertyId';
  static String maintenanceStats(String? propertyId) => 'maintenance_stats_${propertyId ?? 'all'}';
  static String systemStatusByProperty(String propertyId) => 'system_status_$propertyId';
  static String officeAttendance(String officeId, String date) => 'office_attendance_${officeId}_$date';
}
