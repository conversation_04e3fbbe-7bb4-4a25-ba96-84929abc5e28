{"valid_import": true, "imports": [{"uri": "package:flutter/foundation.dart", "transitive": false}, {"uri": "package:frontend/core/services/api_client.dart", "transitive": false}, {"uri": "package:frontend/core/services/auth_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/property_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/dashboard_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/office_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/notification_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/file_service.dart", "transitive": false}], "elements": []}