import { Router } from 'express';
import { PrismaClient, UserRole, MaintenancePriority, MaintenanceStatus, RecurrenceType } from '@prisma/client';
import { authenticate, authorize, requireAction } from '../middleware/auth';
import { validateRequest, asyncHandler } from '../middleware/errorHandler';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createDepartmentSchema = z.object({
  body: z.object({
    name: z.string().min(1, 'Department name is required'),
    description: z.string().optional(),
  }),
});

const createMaintenanceIssueSchema = z.object({
  body: z.object({
    title: z.string().min(1, 'Issue title is required'),
    description: z.string().optional(),
    departmentId: z.string().uuid('Invalid department ID'),
    propertyId: z.string().uuid('Invalid property ID').optional(),
    priority: z.nativeEnum(MaintenancePriority),
    startDate: z.string().datetime('Invalid start date'),
    expectedEndDate: z.string().datetime('Invalid expected end date').optional(),
    assignedTo: z.string().uuid('Invalid assignee ID').optional(),
    isRecurring: z.boolean().default(false),
    recurrenceType: z.nativeEnum(RecurrenceType).optional(),
    remarks: z.string().optional(),
  }),
});

const updateMaintenanceIssueSchema = z.object({
  body: z.object({
    title: z.string().min(1).optional(),
    description: z.string().optional(),
    departmentId: z.string().uuid().optional(),
    propertyId: z.string().uuid().optional(),
    priority: z.nativeEnum(MaintenancePriority).optional(),
    status: z.nativeEnum(MaintenanceStatus).optional(),
    startDate: z.string().datetime().optional(),
    expectedEndDate: z.string().datetime().optional(),
    actualEndDate: z.string().datetime().optional(),
    assignedTo: z.string().uuid().optional(),
    isRecurring: z.boolean().optional(),
    recurrenceType: z.nativeEnum(RecurrenceType).optional(),
    remarks: z.string().optional(),
  }),
});

const createMaintenanceFunctionSchema = z.object({
  body: z.object({
    name: z.string().min(1, 'Function name is required'),
    subFunction: z.string().optional(),
    departmentId: z.string().uuid('Invalid department ID'),
    propertyId: z.string().uuid('Invalid property ID').optional(),
    input: z.string().optional(),
    process: z.string().optional(),
    output: z.string().optional(),
    thresholdLimits: z.string().optional(),
    responsibleAgent: z.string().optional(),
  }),
});

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /maintenance/departments:
 *   get:
 *     tags: [Maintenance]
 *     summary: Get all departments
 *     description: Retrieve all maintenance departments
 *     responses:
 *       200:
 *         description: Departments retrieved successfully
 */
router.get('/departments',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.MAINTENANCE_STAFF]),
  asyncHandler(async (req, res) => {
    const departments = await prisma.department.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            maintenanceIssues: true,
            maintenanceFunctions: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    res.json({
      success: true,
      data: departments,
    });
  })
);

/**
 * @swagger
 * /maintenance/departments:
 *   post:
 *     tags: [Maintenance]
 *     summary: Create new department
 *     description: Create a new maintenance department
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Department created successfully
 */
router.post('/departments',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER], 'canManageUsers'),
  requireAction('create'),
  validateRequest(createDepartmentSchema),
  asyncHandler(async (req, res) => {
    const { name, description } = req.body;

    const department = await prisma.department.create({
      data: {
        name,
        description,
      },
    });

    res.status(201).json({
      success: true,
      data: department,
      message: 'Department created successfully',
    });
  })
);

/**
 * @swagger
 * /maintenance/issues:
 *   get:
 *     tags: [Maintenance]
 *     summary: Get maintenance issues
 *     description: Retrieve maintenance issues with filtering and pagination
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *       - in: query
 *         name: departmentId
 *         schema:
 *           type: string
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Issues retrieved successfully
 */
router.get('/issues',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.MAINTENANCE_STAFF]),
  asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const { status, priority, departmentId, propertyId, search } = req.query;

    const where: any = {};

    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (departmentId) where.departmentId = departmentId;
    if (propertyId) where.propertyId = propertyId;
    if (search) {
      where.OR = [
        { title: { contains: search as string, mode: 'insensitive' } },
        { description: { contains: search as string, mode: 'insensitive' } },
      ];
    }

    const [issues, total] = await Promise.all([
      prisma.maintenanceIssue.findMany({
        where,
        include: {
          department: true,
          property: { select: { id: true, name: true } },
          reporter: { select: { id: true, name: true, email: true } },
          assignee: { select: { id: true, name: true, email: true } },
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.maintenanceIssue.count({ where }),
    ]);

    res.json({
      success: true,
      data: issues,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  })
);

/**
 * @swagger
 * /maintenance/issues:
 *   post:
 *     tags: [Maintenance]
 *     summary: Create maintenance issue
 *     description: Submit a new maintenance issue
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - departmentId
 *               - priority
 *               - startDate
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               departmentId:
 *                 type: string
 *               propertyId:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT]
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               expectedEndDate:
 *                 type: string
 *                 format: date-time
 *               assignedTo:
 *                 type: string
 *               isRecurring:
 *                 type: boolean
 *               recurrenceType:
 *                 type: string
 *                 enum: [DAILY, WEEKLY, MONTHLY, QUARTERLY, YEARLY]
 *               remarks:
 *                 type: string
 *     responses:
 *       201:
 *         description: Issue created successfully
 */
router.post('/issues',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.MAINTENANCE_STAFF]),
  requireAction('create'),
  validateRequest(createMaintenanceIssueSchema),
  asyncHandler(async (req, res) => {
    const issueData = req.body;
    const user = req.user!;

    // Calculate next due date for recurring issues
    let nextDueDate = null;
    if (issueData.isRecurring && issueData.recurrenceType) {
      const startDate = new Date(issueData.startDate);
      switch (issueData.recurrenceType) {
        case 'DAILY':
          nextDueDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'WEEKLY':
          nextDueDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'MONTHLY':
          nextDueDate = new Date(startDate);
          nextDueDate.setMonth(nextDueDate.getMonth() + 1);
          break;
        case 'QUARTERLY':
          nextDueDate = new Date(startDate);
          nextDueDate.setMonth(nextDueDate.getMonth() + 3);
          break;
        case 'YEARLY':
          nextDueDate = new Date(startDate);
          nextDueDate.setFullYear(nextDueDate.getFullYear() + 1);
          break;
      }
    }

    const issue = await prisma.maintenanceIssue.create({
      data: {
        ...issueData,
        startDate: new Date(issueData.startDate),
        expectedEndDate: issueData.expectedEndDate ? new Date(issueData.expectedEndDate) : null,
        reportedBy: user.id,
        nextDueDate,
      },
      include: {
        department: true,
        property: { select: { id: true, name: true } },
        reporter: { select: { id: true, name: true, email: true } },
        assignee: { select: { id: true, name: true, email: true } },
      },
    });

    res.status(201).json({
      success: true,
      data: issue,
      message: 'Maintenance issue created successfully',
    });
  })
);

/**
 * @swagger
 * /maintenance/issues/{id}:
 *   get:
 *     tags: [Maintenance]
 *     summary: Get maintenance issue by ID
 *     description: Retrieve a specific maintenance issue
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Issue retrieved successfully
 *       404:
 *         description: Issue not found
 */
router.get('/issues/:id',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.MAINTENANCE_STAFF]),
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const issue = await prisma.maintenanceIssue.findUnique({
      where: { id },
      include: {
        department: true,
        property: { select: { id: true, name: true, address: true } },
        reporter: { select: { id: true, name: true, email: true } },
        assignee: { select: { id: true, name: true, email: true } },
      },
    });

    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found',
      });
    }

    res.json({
      success: true,
      data: issue,
    });
  })
);

/**
 * @swagger
 * /maintenance/issues/{id}:
 *   put:
 *     tags: [Maintenance]
 *     summary: Update maintenance issue
 *     description: Update an existing maintenance issue
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               departmentId:
 *                 type: string
 *               propertyId:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT]
 *               status:
 *                 type: string
 *                 enum: [OPEN, IN_PROGRESS, COMPLETED, RESOLVED, CLOSED, ON_HOLD]
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               expectedEndDate:
 *                 type: string
 *                 format: date-time
 *               actualEndDate:
 *                 type: string
 *                 format: date-time
 *               assignedTo:
 *                 type: string
 *               isRecurring:
 *                 type: boolean
 *               recurrenceType:
 *                 type: string
 *                 enum: [DAILY, WEEKLY, MONTHLY, QUARTERLY, YEARLY]
 *               remarks:
 *                 type: string
 *     responses:
 *       200:
 *         description: Issue updated successfully
 *       404:
 *         description: Issue not found
 */
router.put('/issues/:id',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.MAINTENANCE_STAFF]),
  requireAction('update'),
  validateRequest(updateMaintenanceIssueSchema),
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    // Check if issue exists
    const existingIssue = await prisma.maintenanceIssue.findUnique({
      where: { id },
    });

    if (!existingIssue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found',
      });
    }

    // Convert date strings to Date objects
    const processedData: any = { ...updateData };
    if (updateData.startDate) processedData.startDate = new Date(updateData.startDate);
    if (updateData.expectedEndDate) processedData.expectedEndDate = new Date(updateData.expectedEndDate);
    if (updateData.actualEndDate) processedData.actualEndDate = new Date(updateData.actualEndDate);

    // Calculate next due date for recurring issues
    if (updateData.isRecurring && updateData.recurrenceType && updateData.startDate) {
      const startDate = new Date(updateData.startDate);
      switch (updateData.recurrenceType) {
        case 'DAILY':
          processedData.nextDueDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'WEEKLY':
          processedData.nextDueDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'MONTHLY':
          processedData.nextDueDate = new Date(startDate);
          processedData.nextDueDate.setMonth(processedData.nextDueDate.getMonth() + 1);
          break;
        case 'QUARTERLY':
          processedData.nextDueDate = new Date(startDate);
          processedData.nextDueDate.setMonth(processedData.nextDueDate.getMonth() + 3);
          break;
        case 'YEARLY':
          processedData.nextDueDate = new Date(startDate);
          processedData.nextDueDate.setFullYear(processedData.nextDueDate.getFullYear() + 1);
          break;
      }
    }

    const updatedIssue = await prisma.maintenanceIssue.update({
      where: { id },
      data: processedData,
      include: {
        department: true,
        property: { select: { id: true, name: true, address: true } },
        reporter: { select: { id: true, name: true, email: true } },
        assignee: { select: { id: true, name: true, email: true } },
      },
    });

    res.json({
      success: true,
      data: updatedIssue,
      message: 'Maintenance issue updated successfully',
    });
  })
);

/**
 * @swagger
 * /maintenance/functions:
 *   get:
 *     tags: [Maintenance]
 *     summary: Get maintenance functions
 *     description: Retrieve maintenance functions with filtering and pagination
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: departmentId
 *         schema:
 *           type: string
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Functions retrieved successfully
 */
router.get('/functions',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.MAINTENANCE_STAFF]),
  asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const { departmentId, propertyId, search } = req.query;

    const where: any = { isActive: true };

    if (departmentId) where.departmentId = departmentId;
    if (propertyId) where.propertyId = propertyId;
    if (search) {
      where.OR = [
        { name: { contains: search as string, mode: 'insensitive' } },
        { subFunction: { contains: search as string, mode: 'insensitive' } },
      ];
    }

    const [functions, total] = await Promise.all([
      prisma.maintenanceFunction.findMany({
        where,
        include: {
          department: true,
          property: { select: { id: true, name: true } },
        },
        orderBy: { name: 'asc' },
        skip: offset,
        take: limit,
      }),
      prisma.maintenanceFunction.count({ where }),
    ]);

    res.json({
      success: true,
      data: functions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  })
);

/**
 * @swagger
 * /maintenance/functions:
 *   post:
 *     tags: [Maintenance]
 *     summary: Create maintenance function
 *     description: Create a new maintenance function process
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - departmentId
 *             properties:
 *               name:
 *                 type: string
 *               subFunction:
 *                 type: string
 *               departmentId:
 *                 type: string
 *               propertyId:
 *                 type: string
 *               input:
 *                 type: string
 *               process:
 *                 type: string
 *               output:
 *                 type: string
 *               thresholdLimits:
 *                 type: string
 *               responsibleAgent:
 *                 type: string
 *     responses:
 *       201:
 *         description: Function created successfully
 */
router.post('/functions',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER], 'canManageUsers'),
  requireAction('create'),
  validateRequest(createMaintenanceFunctionSchema),
  asyncHandler(async (req, res) => {
    const functionData = req.body;

    const maintenanceFunction = await prisma.maintenanceFunction.create({
      data: functionData,
      include: {
        department: true,
        property: { select: { id: true, name: true } },
      },
    });

    res.status(201).json({
      success: true,
      data: maintenanceFunction,
      message: 'Maintenance function created successfully',
    });
  })
);

/**
 * @swagger
 * /maintenance/functions/{id}:
 *   put:
 *     tags: [Maintenance]
 *     summary: Update maintenance function
 *     description: Update an existing maintenance function
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               subFunction:
 *                 type: string
 *               departmentId:
 *                 type: string
 *               propertyId:
 *                 type: string
 *               input:
 *                 type: string
 *               process:
 *                 type: string
 *               output:
 *                 type: string
 *               thresholdLimits:
 *                 type: string
 *               responsibleAgent:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Function updated successfully
 *       404:
 *         description: Function not found
 */
router.put('/functions/:id',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER], 'canManageUsers'),
  requireAction('update'),
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    // Check if function exists
    const existingFunction = await prisma.maintenanceFunction.findUnique({
      where: { id },
    });

    if (!existingFunction) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance function not found',
      });
    }

    const updatedFunction = await prisma.maintenanceFunction.update({
      where: { id },
      data: updateData,
      include: {
        department: true,
        property: { select: { id: true, name: true } },
      },
    });

    res.json({
      success: true,
      data: updatedFunction,
      message: 'Maintenance function updated successfully',
    });
  })
);

/**
 * @swagger
 * /maintenance/functions/{id}:
 *   delete:
 *     tags: [Maintenance]
 *     summary: Delete maintenance function
 *     description: Soft delete a maintenance function
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Function deleted successfully
 *       404:
 *         description: Function not found
 */
router.delete('/functions/:id',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER], 'canManageUsers'),
  requireAction('delete'),
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Check if function exists
    const existingFunction = await prisma.maintenanceFunction.findUnique({
      where: { id },
    });

    if (!existingFunction) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance function not found',
      });
    }

    // Soft delete by setting isActive to false
    await prisma.maintenanceFunction.update({
      where: { id },
      data: { isActive: false },
    });

    res.json({
      success: true,
      message: 'Maintenance function deleted successfully',
    });
  })
);

export default router;
