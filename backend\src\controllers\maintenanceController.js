const MaintenanceIssue = require('../models/MaintenanceIssue');
const MaintenanceComment = require('../models/MaintenanceComment');
const MaintenanceActivity = require('../models/MaintenanceActivity');
const MaintenanceAttachment = require('../models/MaintenanceAttachment');
const { validationResult } = require('express-validator');
const mongoose = require('mongoose');

// Get all maintenance issues with filtering and pagination
exports.getMaintenanceIssues = async (req, res) => {
  try {
    const {
      propertyId,
      status,
      priority,
      department,
      assignedTo,
      search,
      page = 1,
      limit = 20,
      sortBy = 'reportedDate',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};

    // Property-based access control
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && req.user.assignedPropertyIds.length > 0) {
        filter.propertyId = { $in: req.user.assignedPropertyIds };
      }
    }

    if (propertyId) {
      filter.propertyId = propertyId;
    }

    if (status) {
      filter.status = status;
    }

    if (priority) {
      filter.priority = priority;
    }

    if (department) {
      filter.department = department;
    }

    if (assignedTo) {
      filter.assignedTo = assignedTo;
    }

    // Text search
    if (search) {
      filter.$text = { $search: search };
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Sort
    const sortObj = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const [issues, total] = await Promise.all([
      MaintenanceIssue.find(filter)
        .populate('property', 'name type address')
        .populate('assignedToUser', 'name email')
        .populate('reportedByUser', 'name email')
        .sort(sortObj)
        .skip(skip)
        .limit(limitNum)
        .lean(),
      MaintenanceIssue.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrevious = pageNum > 1;

    res.json({
      success: true,
      data: {
        data: issues,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages,
        hasNext,
        hasPrevious
      }
    });
  } catch (error) {
    console.error('Error fetching maintenance issues:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch maintenance issues',
      error: error.message
    });
  }
};

// Get single maintenance issue with details
exports.getMaintenanceIssue = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    const issue = await MaintenanceIssue.findById(id)
      .populate('property', 'name type address')
      .populate('assignedToUser', 'name email')
      .populate('reportedByUser', 'name email')
      .populate({
        path: 'comments',
        populate: {
          path: 'user',
          select: 'name email'
        },
        options: { sort: { createdAt: -1 } }
      })
      .populate({
        path: 'activities',
        populate: {
          path: 'user',
          select: 'name email'
        },
        options: { sort: { createdAt: -1 } }
      })
      .populate({
        path: 'attachments',
        populate: {
          path: 'uploader',
          select: 'name email'
        }
      });

    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && 
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this property\'s maintenance data'
        });
      }
    }

    res.json({
      success: true,
      data: issue
    });
  } catch (error) {
    console.error('Error fetching maintenance issue:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch maintenance issue',
      error: error.message
    });
  }
};

// Create new maintenance issue
exports.createMaintenanceIssue = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      propertyId,
      title,
      description,
      priority,
      department,
      assignedTo,
      estimatedCost,
      recurrence
    } = req.body;

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && 
          !req.user.assignedPropertyIds.includes(propertyId)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to create issues for this property'
        });
      }
    }

    // Create the issue
    const issue = new MaintenanceIssue({
      propertyId,
      title,
      description,
      priority,
      department,
      assignedTo: assignedTo || null,
      estimatedCost: estimatedCost || null,
      recurrence: recurrence || 'NONE',
      reportedBy: req.user.id
    });

    await issue.save();

    // Add creation activity
    await issue.addActivity('CREATED', req.user.id);

    // If assigned, add assignment activity
    if (assignedTo) {
      await issue.addActivity('ASSIGNED', req.user.id, null, assignedTo);
    }

    // Populate the response
    await issue.populate([
      { path: 'property', select: 'name type address' },
      { path: 'assignedToUser', select: 'name email' },
      { path: 'reportedByUser', select: 'name email' }
    ]);

    res.status(201).json({
      success: true,
      data: issue,
      message: 'Maintenance issue created successfully'
    });
  } catch (error) {
    console.error('Error creating maintenance issue:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create maintenance issue',
      error: error.message
    });
  }
};

// Update maintenance issue
exports.updateMaintenanceIssue = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && 
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to update this issue'
        });
      }
    }

    // Track changes for activities
    const changes = [];
    
    // Check for status change
    if (updates.status && updates.status !== issue.status) {
      changes.push({
        action: 'STATUS_CHANGED',
        oldValue: issue.status,
        newValue: updates.status
      });
    }

    // Check for priority change
    if (updates.priority && updates.priority !== issue.priority) {
      changes.push({
        action: 'PRIORITY_CHANGED',
        oldValue: issue.priority,
        newValue: updates.priority
      });
    }

    // Check for assignment change
    if (updates.assignedTo !== undefined && updates.assignedTo !== issue.assignedTo?.toString()) {
      changes.push({
        action: 'ASSIGNED',
        oldValue: issue.assignedTo?.toString() || null,
        newValue: updates.assignedTo
      });
    }

    // Update the issue
    Object.assign(issue, updates);
    await issue.save();

    // Add activities for changes
    for (const change of changes) {
      await issue.addActivity(change.action, req.user.id, change.oldValue, change.newValue);
    }

    // If no specific changes tracked, add general update activity
    if (changes.length === 0) {
      await issue.addActivity('UPDATED', req.user.id);
    }

    // Populate the response
    await issue.populate([
      { path: 'property', select: 'name type address' },
      { path: 'assignedToUser', select: 'name email' },
      { path: 'reportedByUser', select: 'name email' }
    ]);

    res.json({
      success: true,
      data: issue,
      message: 'Maintenance issue updated successfully'
    });
  } catch (error) {
    console.error('Error updating maintenance issue:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update maintenance issue',
      error: error.message
    });
  }
};

// Delete maintenance issue
exports.deleteMaintenanceIssue = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check permissions - only admin or issue reporter can delete
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN' && 
        issue.reportedBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to delete this issue'
      });
    }

    // Delete related documents
    await Promise.all([
      MaintenanceComment.deleteMany({ issueId: id }),
      MaintenanceActivity.deleteMany({ issueId: id }),
      MaintenanceAttachment.deleteMany({ issueId: id })
    ]);

    // Delete the issue
    await MaintenanceIssue.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Maintenance issue deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting maintenance issue:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete maintenance issue',
      error: error.message
    });
  }
};

// Get maintenance statistics
exports.getMaintenanceStatistics = async (req, res) => {
  try {
    const { propertyId, dateFrom, dateTo } = req.query;

    // Build filter
    const filter = {};

    // Property-based access control
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds && req.user.assignedPropertyIds.length > 0) {
        filter.propertyId = { $in: req.user.assignedPropertyIds };
      }
    }

    if (propertyId) {
      filter.propertyId = propertyId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      filter.reportedDate = {};
      if (dateFrom) filter.reportedDate.$gte = new Date(dateFrom);
      if (dateTo) filter.reportedDate.$lte = new Date(dateTo);
    }

    const statistics = await MaintenanceIssue.getStatistics(filter);

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('Error fetching maintenance statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch maintenance statistics',
      error: error.message
    });
  }
};

// Add comment to maintenance issue
exports.addComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { comment } = req.body;

    if (!comment || comment.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Comment is required'
      });
    }

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds &&
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to comment on this issue'
        });
      }
    }

    // Add comment
    const commentDoc = await issue.addComment(req.user.id, comment.trim());

    // Add activity
    await issue.addActivity('COMMENT_ADDED', req.user.id);

    // Populate user info
    await commentDoc.populate('user', 'name email');

    res.status(201).json({
      success: true,
      data: commentDoc,
      message: 'Comment added successfully'
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add comment',
      error: error.message
    });
  }
};

// Get comments for maintenance issue
exports.getComments = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    // Check if issue exists and user has access
    const issue = await MaintenanceIssue.findById(id);
    if (!issue) {
      return res.status(404).json({
        success: false,
        message: 'Maintenance issue not found'
      });
    }

    // Check property access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
      if (req.user.assignedPropertyIds &&
          !req.user.assignedPropertyIds.includes(issue.propertyId.toString())) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to view comments for this issue'
        });
      }
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const [comments, total] = await Promise.all([
      MaintenanceComment.find({ issueId: id })
        .populate('user', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum),
      MaintenanceComment.countDocuments({ issueId: id })
    ]);

    const totalPages = Math.ceil(total / limitNum);

    res.json({
      success: true,
      data: {
        data: comments,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrevious: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch comments',
      error: error.message
    });
  }
};
