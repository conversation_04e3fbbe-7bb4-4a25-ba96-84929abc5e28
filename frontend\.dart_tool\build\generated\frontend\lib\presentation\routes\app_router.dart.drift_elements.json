{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/auth/login_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/dashboard_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/properties/properties_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/properties/property_detail_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/office/office_management_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/settings/settings_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/splash/splash_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/systems/water_management_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/systems/electricity_management_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/systems/security_management_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/systems/internet_management_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/systems/ott_services_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/maintenance/maintenance_screen.dart", "transitive": false}], "elements": []}