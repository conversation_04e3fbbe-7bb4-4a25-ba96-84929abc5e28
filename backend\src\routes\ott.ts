import { Router } from 'express';
import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import { 
  createOTTServiceSchema, 
  updateOTTServiceSchema,
  paginationSchema 
} from '@/lib/validation';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, authorize, requireAction } from '@/middleware/auth';
import { conditionalRateLimit } from '@/middleware/rateLimiter';

const router = Router();

// Apply authentication and rate limiting
router.use(authenticate);
router.use(conditionalRateLimit);

/**
 * @swagger
 * /ott/{propertyId}:
 *   get:
 *     tags: [OTT Services]
 *     summary: List OTT services for a property
 *     description: Get list of OTT services for a specific property
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: OTT services retrieved successfully
 */
router.get('/:propertyId',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  validateRequest(paginationSchema, 'query'),
  asyncHandler(async (req, res) => {
    const { propertyId } = req.params;
    const { page, limit } = req.query as any;
    const offset = (page - 1) * limit;

    // Check if property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      throw new AppError('Property not found', 404, 'PROPERTY_NOT_FOUND');
    }

    const [ottServices, total] = await Promise.all([
      prisma.oTTService.findMany({
        where: { 
          propertyId,
          isActive: true 
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.oTTService.count({
        where: { 
          propertyId,
          isActive: true 
        },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: ottServices,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /ott/{propertyId}:
 *   post:
 *     tags: [OTT Services]
 *     summary: Create new OTT service
 *     description: Add a new OTT service to a property
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateOTTServiceRequest'
 *     responses:
 *       201:
 *         description: OTT service created successfully
 */
router.post('/:propertyId',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  requireAction('create'),
  validateRequest(createOTTServiceSchema),
  asyncHandler(async (req, res) => {
    const { propertyId } = req.params;
    const { platform, plan, loginId, password, nextPayment, status } = req.body;

    // Check if property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      throw new AppError('Property not found', 404, 'PROPERTY_NOT_FOUND');
    }

    const ottService = await prisma.oTTService.create({
      data: {
        propertyId,
        platform,
        plan,
        loginId,
        password,
        nextPayment: nextPayment ? new Date(nextPayment) : null,
        status: status || 'PENDING',
      },
    });

    res.status(201).json({
      success: true,
      data: ottService,
      message: 'OTT service created successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /ott/{propertyId}/{serviceId}:
 *   put:
 *     tags: [OTT Services]
 *     summary: Update OTT service
 *     description: Update an existing OTT service
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateOTTServiceRequest'
 *     responses:
 *       200:
 *         description: OTT service updated successfully
 */
router.put('/:propertyId/:serviceId',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  requireAction('update'),
  validateRequest(updateOTTServiceSchema),
  asyncHandler(async (req, res) => {
    const { propertyId, serviceId } = req.params;
    const { platform, plan, loginId, password, nextPayment, status } = req.body;

    // Check if service exists and belongs to the property
    const existingService = await prisma.oTTService.findFirst({
      where: { 
        id: serviceId,
        propertyId,
        isActive: true 
      },
    });

    if (!existingService) {
      throw new AppError('OTT service not found', 404, 'OTT_SERVICE_NOT_FOUND');
    }

    const updateData: any = {};
    if (platform !== undefined) updateData.platform = platform;
    if (plan !== undefined) updateData.plan = plan;
    if (loginId !== undefined) updateData.loginId = loginId;
    if (password !== undefined) updateData.password = password;
    if (nextPayment !== undefined) updateData.nextPayment = nextPayment ? new Date(nextPayment) : null;
    if (status !== undefined) updateData.status = status;

    const ottService = await prisma.oTTService.update({
      where: { id: serviceId },
      data: updateData,
    });

    res.json({
      success: true,
      data: ottService,
      message: 'OTT service updated successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /ott/{propertyId}/{serviceId}:
 *   delete:
 *     tags: [OTT Services]
 *     summary: Delete OTT service
 *     description: Soft delete an OTT service
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: OTT service deleted successfully
 */
router.delete('/:propertyId/:serviceId',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  requireAction('delete'),
  asyncHandler(async (req, res) => {
    const { propertyId, serviceId } = req.params;

    // Check if service exists and belongs to the property
    const existingService = await prisma.oTTService.findFirst({
      where: { 
        id: serviceId,
        propertyId,
        isActive: true 
      },
    });

    if (!existingService) {
      throw new AppError('OTT service not found', 404, 'OTT_SERVICE_NOT_FOUND');
    }

    // Soft delete
    await prisma.oTTService.update({
      where: { id: serviceId },
      data: { isActive: false },
    });

    res.json({
      success: true,
      message: 'OTT service deleted successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
