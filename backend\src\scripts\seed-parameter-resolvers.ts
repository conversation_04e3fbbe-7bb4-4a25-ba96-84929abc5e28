import { PrismaClient } from '@prisma/client';
import { PARAMETER_RESOLVERS, BREADCRUMB_TEMPLATES } from '../config/parameter-resolvers';
import { BREADCRUMB_PATHS } from '../config/breadcrumb-paths';

const prisma = new PrismaClient();

async function seedParameterResolvers() {
  console.log('🌱 Seeding parameter resolvers and breadcrumb paths...');

  try {
    // 1. Seed parameter resolvers
    console.log('📋 Seeding parameter resolvers...');
    for (const resolver of PARAMETER_RESOLVERS) {
      await prisma.parameterResolver.upsert({
        where: { parameterName: resolver.parameterName },
        update: {
          sourceTable: resolver.sourceTable,
          sourceField: resolver.sourceField,
          keyField: resolver.keyField,
          cacheMinutes: resolver.cacheMinutes,
          metadata: resolver.metadata || {},
        },
        create: {
          parameterName: resolver.parameterName,
          sourceTable: resolver.sourceTable,
          sourceField: resolver.sourceField,
          keyField: resolver.keyField,
          cacheMinutes: resolver.cacheMinutes,
          metadata: resolver.metadata || {},
        },
      });
    }
    console.log(`✅ Seeded ${PARAMETER_RESOLVERS.length} parameter resolvers`);

    // 2. Seed breadcrumb paths
    console.log('🗺️ Seeding breadcrumb paths...');
    for (const pathDef of BREADCRUMB_PATHS) {
      const breadcrumbPath = await prisma.breadcrumbPath.upsert({
        where: { path: pathDef.path },
        update: {
          name: pathDef.name,
          description: pathDef.description,
          level: pathDef.level,
          parentPath: pathDef.parentPath,
          metadata: pathDef.metadata || {},
        },
        create: {
          path: pathDef.path,
          name: pathDef.name,
          description: pathDef.description,
          level: pathDef.level,
          parentPath: pathDef.parentPath,
          metadata: pathDef.metadata || {},
        },
      });

      // 3. Seed path components
      if (pathDef.components) {
        for (const component of pathDef.components) {
          await prisma.pathComponent.upsert({
            where: {
              pathId_componentId: {
                pathId: breadcrumbPath.id,
                componentId: component.componentId,
              },
            },
            update: {
              name: component.name,
              type: component.type,
              section: component.section,
              permissions: component.permissions,
              metadata: component.metadata || {},
            },
            create: {
              pathId: breadcrumbPath.id,
              componentId: component.componentId,
              name: component.name,
              type: component.type,
              section: component.section,
              permissions: component.permissions,
              metadata: component.metadata || {},
            },
          });
        }
      }
    }
    console.log(`✅ Seeded ${BREADCRUMB_PATHS.length} breadcrumb paths`);

    // 4. Seed breadcrumb rules from templates
    console.log('📝 Seeding breadcrumb rules...');
    for (const template of BREADCRUMB_TEMPLATES) {
      await prisma.breadcrumbRule.upsert({
        where: { pattern: template.pathPattern },
        update: {
          template: template.displayTemplate,
          resolver: 'default',
          metadata: {
            description: template.description,
            examples: template.examples,
          },
        },
        create: {
          pattern: template.pathPattern,
          template: template.displayTemplate,
          resolver: 'default',
          metadata: {
            description: template.description,
            examples: template.examples,
          },
        },
      });
    }
    console.log(`✅ Seeded ${BREADCRUMB_TEMPLATES.length} breadcrumb rules`);

    // 5. Create some sample resolved breadcrumbs for testing
    console.log('🧪 Creating sample breadcrumb cache entries...');
    await createSampleBreadcrumbCache();

    console.log('🎉 Parameter resolvers and breadcrumb paths seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding parameter resolvers:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function createSampleBreadcrumbCache() {
  try {
    // Get some sample properties for caching
    const properties = await prisma.property.findMany({
      take: 3,
      select: { id: true, name: true }
    });

    if (properties.length === 0) {
      console.log('⚠️ No properties found for sample cache creation');
      return;
    }

    const samplePaths = [
      {
        pattern: '/properties/{propertyId}',
        template: '{propertyName}',
      },
      {
        pattern: '/properties/{propertyId}/systems',
        template: '{propertyName} > Systems',
      },
      {
        pattern: '/properties/{propertyId}/systems/security',
        template: '{propertyName} > Security',
      },
      {
        pattern: '/properties/{propertyId}/systems/security/cctv',
        template: '{propertyName} > Security > CCTV',
      },
    ];

    for (const property of properties) {
      for (const pathInfo of samplePaths) {
        const resolvedTemplate = pathInfo.template.replace('{propertyName}', property.name);
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

        await prisma.breadcrumbCache.create({
          data: {
            pathPattern: pathInfo.pattern,
            resolvedPath: resolvedTemplate,
            parameters: { propertyId: property.id },
            displayName: resolvedTemplate.split(' > ').pop() || resolvedTemplate,
            expiresAt,
          },
        });
      }
    }

    console.log(`✅ Created sample cache entries for ${properties.length} properties`);

  } catch (error) {
    console.error('Error creating sample breadcrumb cache:', error);
  }
}

// Helper function to validate parameter resolvers
async function validateParameterResolvers() {
  console.log('🔍 Validating parameter resolvers...');

  const resolvers = await prisma.parameterResolver.findMany();
  const validationResults = [];

  for (const resolver of resolvers) {
    try {
      // Test if the source table exists and has the required fields
      const testQuery = `
        SELECT "${resolver.sourceField}" as display_value 
        FROM "${resolver.sourceTable}" 
        WHERE "${resolver.keyField}" IS NOT NULL 
        LIMIT 1
      `;

      const result = await prisma.$queryRawUnsafe(testQuery);
      
      validationResults.push({
        parameterName: resolver.parameterName,
        status: 'valid',
        message: 'Resolver configuration is valid'
      });

    } catch (error) {
      validationResults.push({
        parameterName: resolver.parameterName,
        status: 'error',
        message: `Validation failed: ${error.message}`
      });
    }
  }

  console.log('📊 Validation Results:');
  validationResults.forEach(result => {
    const icon = result.status === 'valid' ? '✅' : '❌';
    console.log(`${icon} ${result.parameterName}: ${result.message}`);
  });

  return validationResults;
}

// Helper function to test breadcrumb resolution
async function testBreadcrumbResolution() {
  console.log('🧪 Testing breadcrumb resolution...');

  try {
    // Get a sample property
    const property = await prisma.property.findFirst({
      select: { id: true, name: true }
    });

    if (!property) {
      console.log('⚠️ No properties found for testing');
      return;
    }

    // Test parameter resolution
    const resolvers = await prisma.parameterResolver.findMany({
      where: { parameterName: 'propertyId' }
    });

    if (resolvers.length > 0) {
      const resolver = resolvers[0];
      const testQuery = `
        SELECT "${resolver.sourceField}" as display_value 
        FROM "${resolver.sourceTable}" 
        WHERE "${resolver.keyField}" = $1 
        LIMIT 1
      `;

      const result = await prisma.$queryRawUnsafe(testQuery, property.id);
      
      if (Array.isArray(result) && result.length > 0) {
        const resolvedName = (result[0] as any).display_value;
        console.log(`✅ Parameter resolution test: ${property.id} → ${resolvedName}`);
      } else {
        console.log(`❌ Parameter resolution test failed for ${property.id}`);
      }
    }

  } catch (error) {
    console.error('Error testing breadcrumb resolution:', error);
  }
}

// Run the seeding
if (require.main === module) {
  seedParameterResolvers()
    .then(() => validateParameterResolvers())
    .then(() => testBreadcrumbResolution())
    .catch((error) => {
      console.error('Failed to seed parameter resolvers:', error);
      process.exit(1);
    });
}

export { seedParameterResolvers, validateParameterResolvers, testBreadcrumbResolution };
