import { prisma } from '@/lib/prisma';

export interface ResolvedBreadcrumbItem {
  path: string;
  originalPath: string;
  displayName: string;
  level: number;
  parameters: Record<string, string>;
  resolvedParameters: Record<string, string>;
}

export interface BreadcrumbResolutionContext {
  userId: string;
  role: string;
  parameters: Record<string, string>;
  metadata?: Record<string, any>;
}

export class BreadcrumbResolverService {
  
  /**
   * Resolve dynamic breadcrumb path with human-readable names
   */
  static async resolveBreadcrumbPath(
    pathPattern: string,
    context: BreadcrumbResolutionContext
  ): Promise<ResolvedBreadcrumbItem[]> {
    try {
      // Check cache first
      const cached = await this.getCachedBreadcrumb(pathPattern, context.parameters);
      if (cached) {
        return this.buildBreadcrumbHierarchy(cached.resolvedPath, cached.displayName, context);
      }

      // Build breadcrumb hierarchy
      const pathParts = pathPattern.split('/').filter(part => part);
      const breadcrumbs: ResolvedBreadcrumbItem[] = [];
      let currentPath = '';

      for (let i = 0; i < pathParts.length; i++) {
        currentPath += '/' + pathParts[i];
        
        // Get breadcrumb definition
        const breadcrumbPath = await prisma.breadcrumbPath.findUnique({
          where: { path: currentPath }
        });

        if (breadcrumbPath) {
          const resolvedItem = await this.resolveBreadcrumbItem(
            breadcrumbPath,
            context.parameters,
            i
          );
          breadcrumbs.push(resolvedItem);
        }
      }

      // Cache the result
      if (breadcrumbs.length > 0) {
        await this.cacheBreadcrumb(pathPattern, breadcrumbs, context.parameters);
      }

      return breadcrumbs;

    } catch (error) {
      console.error('Error resolving breadcrumb path:', error);
      return [];
    }
  }

  /**
   * Resolve individual breadcrumb item
   */
  private static async resolveBreadcrumbItem(
    breadcrumbPath: any,
    parameters: Record<string, string>,
    level: number
  ): Promise<ResolvedBreadcrumbItem> {
    const resolvedParameters: Record<string, string> = {};
    let displayName = breadcrumbPath.name;

    // Find parameters in the path
    const paramMatches = breadcrumbPath.path.match(/\{([^}]+)\}/g);
    
    if (paramMatches) {
      for (const paramMatch of paramMatches) {
        const paramName = paramMatch.slice(1, -1); // Remove { }
        const paramValue = parameters[paramName];
        
        if (paramValue) {
          const resolvedValue = await this.resolveParameter(paramName, paramValue);
          resolvedParameters[paramName] = resolvedValue;
          
          // Replace parameter in display name
          displayName = displayName.replace(
            new RegExp(`\\{${paramName}\\}`, 'g'),
            resolvedValue
          );
        }
      }
    }

    return {
      path: breadcrumbPath.path,
      originalPath: breadcrumbPath.path,
      displayName,
      level,
      parameters,
      resolvedParameters,
    };
  }

  /**
   * Resolve parameter value to human-readable name
   */
  private static async resolveParameter(paramName: string, paramValue: string): Promise<string> {
    try {
      // Get resolver configuration
      const resolver = await prisma.parameterResolver.findUnique({
        where: { parameterName: paramName }
      });

      if (!resolver) {
        console.warn(`No resolver found for parameter: ${paramName}`);
        return paramValue; // Return original value if no resolver
      }

      // Build dynamic query based on resolver configuration
      const result = await this.executeParameterQuery(resolver, paramValue);
      return result || paramValue;

    } catch (error) {
      console.error(`Error resolving parameter ${paramName}:`, error);
      return paramValue; // Fallback to original value
    }
  }

  /**
   * Execute parameter resolution query
   */
  private static async executeParameterQuery(resolver: any, paramValue: string): Promise<string | null> {
    try {
      // Use Prisma's raw query capability for dynamic table access
      const query = `
        SELECT "${resolver.sourceField}" as display_value 
        FROM "${resolver.sourceTable}" 
        WHERE "${resolver.keyField}" = $1 
        LIMIT 1
      `;

      const result = await prisma.$queryRawUnsafe(query, paramValue);
      
      if (Array.isArray(result) && result.length > 0) {
        return (result[0] as any).display_value;
      }

      return null;

    } catch (error) {
      console.error('Error executing parameter query:', error);
      return null;
    }
  }

  /**
   * Get cached breadcrumb if available and not expired
   */
  private static async getCachedBreadcrumb(
    pathPattern: string,
    parameters: Record<string, string>
  ): Promise<any | null> {
    try {
      const cached = await prisma.breadcrumbCache.findFirst({
        where: {
          pathPattern,
          parameters: parameters,
          expiresAt: { gt: new Date() }
        }
      });

      return cached;

    } catch (error) {
      console.error('Error getting cached breadcrumb:', error);
      return null;
    }
  }

  /**
   * Cache breadcrumb resolution result
   */
  private static async cacheBreadcrumb(
    pathPattern: string,
    breadcrumbs: ResolvedBreadcrumbItem[],
    parameters: Record<string, string>
  ): Promise<void> {
    try {
      const lastBreadcrumb = breadcrumbs[breadcrumbs.length - 1];
      const resolvedPath = breadcrumbs.map(b => b.displayName).join(' > ');
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

      await prisma.breadcrumbCache.upsert({
        where: {
          pathPattern_parameters: {
            pathPattern,
            parameters
          }
        },
        update: {
          resolvedPath,
          displayName: lastBreadcrumb.displayName,
          expiresAt
        },
        create: {
          pathPattern,
          resolvedPath,
          parameters,
          displayName: lastBreadcrumb.displayName,
          expiresAt
        }
      });

    } catch (error) {
      console.error('Error caching breadcrumb:', error);
    }
  }

  /**
   * Build breadcrumb hierarchy from cached data
   */
  private static buildBreadcrumbHierarchy(
    resolvedPath: string,
    displayName: string,
    context: BreadcrumbResolutionContext
  ): ResolvedBreadcrumbItem[] {
    const parts = resolvedPath.split(' > ');
    return parts.map((part, index) => ({
      path: '', // Would need to reconstruct from cache
      originalPath: '',
      displayName: part,
      level: index,
      parameters: context.parameters,
      resolvedParameters: {},
    }));
  }

  /**
   * Get breadcrumb suggestions for autocomplete/search
   */
  static async getBreadcrumbSuggestions(
    query: string,
    context: BreadcrumbResolutionContext,
    limit: number = 10
  ): Promise<ResolvedBreadcrumbItem[]> {
    try {
      // Search in cached breadcrumbs first
      const cached = await prisma.breadcrumbCache.findMany({
        where: {
          OR: [
            { resolvedPath: { contains: query, mode: 'insensitive' } },
            { displayName: { contains: query, mode: 'insensitive' } }
          ],
          expiresAt: { gt: new Date() }
        },
        take: limit,
        orderBy: { createdAt: 'desc' }
      });

      return cached.map(item => ({
        path: item.pathPattern,
        originalPath: item.pathPattern,
        displayName: item.displayName,
        level: 0,
        parameters: item.parameters as Record<string, string>,
        resolvedParameters: {},
      }));

    } catch (error) {
      console.error('Error getting breadcrumb suggestions:', error);
      return [];
    }
  }

  /**
   * Preload common breadcrumb resolutions
   */
  static async preloadCommonBreadcrumbs(userId: string): Promise<void> {
    try {
      // Get user's assigned properties
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { assignedProperties: true, role: true }
      });

      if (!user) return;

      // Preload breadcrumbs for assigned properties
      const commonPaths = [
        '/properties/{propertyId}',
        '/properties/{propertyId}/systems',
        '/properties/{propertyId}/systems/security',
        '/properties/{propertyId}/systems/electricity',
        '/properties/{propertyId}/systems/water',
      ];

      for (const propertyId of user.assignedProperties) {
        for (const pathPattern of commonPaths) {
          const context: BreadcrumbResolutionContext = {
            userId,
            role: user.role,
            parameters: { propertyId }
          };

          // This will cache the resolution
          await this.resolveBreadcrumbPath(pathPattern, context);
        }
      }

    } catch (error) {
      console.error('Error preloading breadcrumbs:', error);
    }
  }

  /**
   * Clean expired cache entries
   */
  static async cleanExpiredCache(): Promise<void> {
    try {
      await prisma.breadcrumbCache.deleteMany({
        where: {
          expiresAt: { lt: new Date() }
        }
      });
    } catch (error) {
      console.error('Error cleaning expired cache:', error);
    }
  }
}
