import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/system.dart';
import '../../data/repositories/system_repository.dart';
import '../../core/services/service_locator.dart';
import '../../core/utils/cache_manager.dart';
import '../../core/utils/connectivity_manager.dart';

// System Repository Provider
final systemRepositoryProvider = Provider<SystemRepository>((ref) {
  return serviceLocator.systemRepository;
});

// System Statuses Provider with filtering
final systemStatusesProvider = FutureProvider.family<List<SystemStatus>, SystemStatusParams>((ref, params) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'system_statuses_${params.hashCode}';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getSystemStatuses(
        propertyId: params.propertyId,
        systemType: params.systemType,
        status: params.status,
      );
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 2), // Short cache for system status
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<List<SystemStatus>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // If no cache and offline, return empty list
    if (!connectivityManager.isConnected) {
      return [];
    }
    
    throw Exception('Failed to load system statuses');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<List<SystemStatus>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return [];
  }
});

// Water Systems Provider
final waterSystemsProvider = FutureProvider.family<WaterSystemsResponse, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'water_systems_$propertyId';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getWaterSystems(propertyId);
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5), // Medium cache for water systems
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<WaterSystemsResponse>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // If no cache and offline, return empty response
    if (!connectivityManager.isConnected) {
      return WaterSystemsResponse.empty();
    }
    
    throw Exception('Failed to load water systems');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<WaterSystemsResponse>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return WaterSystemsResponse.empty();
  }
});

// Electricity Systems Provider
final electricitySystemsProvider = FutureProvider.family<ElectricitySystemsResponse, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'electricity_systems_$propertyId';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getElectricitySystems(propertyId);
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5), // Medium cache for electricity systems
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<ElectricitySystemsResponse>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // If no cache and offline, return empty response
    if (!connectivityManager.isConnected) {
      return ElectricitySystemsResponse.empty();
    }
    
    throw Exception('Failed to load electricity systems');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<ElectricitySystemsResponse>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return ElectricitySystemsResponse.empty();
  }
});

// Security Systems Provider
final securitySystemsProvider = FutureProvider.family<SecuritySystemsResponse, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'security_systems_$propertyId';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isConnected) {
      final response = await repository.getSecuritySystems(propertyId);
      
      if (response.success && response.data != null) {
        // Cache the fresh data
        await cacheManager.set(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5), // Medium cache for security systems
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.get<SecuritySystemsResponse>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    // If no cache and offline, return empty response
    if (!connectivityManager.isConnected) {
      return SecuritySystemsResponse.empty();
    }
    
    throw Exception('Failed to load security systems');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.get<SecuritySystemsResponse>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }
    
    return SecuritySystemsResponse.empty();
  }
});

// Update System Status Provider
final updateSystemStatusProvider = FutureProvider.family<bool, UpdateSystemStatusParams>((ref, params) async {
  final repository = ref.read(systemRepositoryProvider);
  
  try {
    final response = await repository.updateSystemStatus(
      id: params.id,
      status: params.status,
      description: params.description,
      healthScore: params.healthScore,
      metadata: params.metadata,
    );
    
    if (response.success) {
      // Invalidate related providers to refresh data
      ref.invalidate(systemStatusesProvider);
      ref.invalidate(waterSystemsProvider);
      ref.invalidate(electricitySystemsProvider);
      ref.invalidate(securitySystemsProvider);
      return true;
    }
    
    return false;
  } catch (e) {
    return false;
  }
});

// System Health Summary Provider (computed from all systems)
final systemHealthSummaryProvider = Provider.family<SystemHealthSummary, String>((ref, propertyId) {
  final waterSystemsAsyncValue = ref.watch(waterSystemsProvider(propertyId));
  final electricitySystemsAsyncValue = ref.watch(electricitySystemsProvider(propertyId));
  final securitySystemsAsyncValue = ref.watch(securitySystemsProvider(propertyId));

  return SystemHealthSummary(
    waterHealth: waterSystemsAsyncValue.when(
      data: (data) => _calculateWaterHealth(data),
      loading: () => SystemHealth.unknown(),
      error: (_, __) => SystemHealth.unknown(),
    ),
    electricityHealth: electricitySystemsAsyncValue.when(
      data: (data) => _calculateElectricityHealth(data),
      loading: () => SystemHealth.unknown(),
      error: (_, __) => SystemHealth.unknown(),
    ),
    securityHealth: securitySystemsAsyncValue.when(
      data: (data) => _calculateSecurityHealth(data),
      loading: () => SystemHealth.unknown(),
      error: (_, __) => SystemHealth.unknown(),
    ),
  );
});

// System Status Parameters
class SystemStatusParams {
  final String? propertyId;
  final String? systemType;
  final String? status;

  const SystemStatusParams({
    this.propertyId,
    this.systemType,
    this.status,
  });

  @override
  int get hashCode => Object.hash(propertyId, systemType, status);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SystemStatusParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          systemType == other.systemType &&
          status == other.status;
}

// Update System Status Parameters
class UpdateSystemStatusParams {
  final String id;
  final String status;
  final String? description;
  final int? healthScore;
  final Map<String, dynamic>? metadata;

  const UpdateSystemStatusParams({
    required this.id,
    required this.status,
    this.description,
    this.healthScore,
    this.metadata,
  });
}

// System Health Summary Model
class SystemHealthSummary {
  final SystemHealth waterHealth;
  final SystemHealth electricityHealth;
  final SystemHealth securityHealth;

  const SystemHealthSummary({
    required this.waterHealth,
    required this.electricityHealth,
    required this.securityHealth,
  });

  int get overallHealthScore {
    final scores = [
      waterHealth.healthScore,
      electricityHealth.healthScore,
      securityHealth.healthScore,
    ].where((score) => score > 0);
    
    if (scores.isEmpty) return 0;
    return (scores.reduce((a, b) => a + b) / scores.length).round();
  }

  String get overallStatus {
    final statuses = [waterHealth.status, electricityHealth.status, securityHealth.status];
    
    if (statuses.contains('CRITICAL')) return 'CRITICAL';
    if (statuses.contains('WARNING')) return 'WARNING';
    if (statuses.contains('OPERATIONAL')) return 'OPERATIONAL';
    return 'UNKNOWN';
  }
}

// System Health Model
class SystemHealth {
  final String status;
  final int healthScore;
  final int operational;
  final int total;
  final String? lastUpdate;

  const SystemHealth({
    required this.status,
    required this.healthScore,
    required this.operational,
    required this.total,
    this.lastUpdate,
  });

  factory SystemHealth.unknown() {
    return const SystemHealth(
      status: 'UNKNOWN',
      healthScore: 0,
      operational: 0,
      total: 0,
    );
  }
}

// Helper functions to calculate system health
SystemHealth _calculateWaterHealth(WaterSystemsResponse data) {
  if (data.systems.isEmpty) return SystemHealth.unknown();
  
  final operational = data.systems.where((s) => s.pumpStatus == 'ON').length;
  final total = data.systems.length;
  final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;
  
  String status = 'OPERATIONAL';
  if (healthScore < 50) {
    status = 'CRITICAL';
  } else if (healthScore < 80) {
    status = 'WARNING';
  }
  
  return SystemHealth(
    status: status,
    healthScore: healthScore,
    operational: operational,
    total: total,
    lastUpdate: DateTime.now().toIso8601String(),
  );
}

SystemHealth _calculateElectricityHealth(ElectricitySystemsResponse data) {
  if (data.systems.isEmpty) return SystemHealth.unknown();
  
  final operational = data.systems.where((s) => 
    s.generatorStatus == 'ON' || s.mainsPowerStatus == 'AVAILABLE'
  ).length;
  final total = data.systems.length;
  final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;
  
  String status = 'OPERATIONAL';
  if (healthScore < 50) {
    status = 'CRITICAL';
  } else if (healthScore < 80) {
    status = 'WARNING';
  }
  
  return SystemHealth(
    status: status,
    healthScore: healthScore,
    operational: operational,
    total: total,
    lastUpdate: DateTime.now().toIso8601String(),
  );
}

SystemHealth _calculateSecurityHealth(SecuritySystemsResponse data) {
  if (data.systems.isEmpty) return SystemHealth.unknown();
  
  final operational = data.systems.where((s) => s.alarmStatus == 'ARMED').length;
  final total = data.systems.length;
  final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;
  
  String status = 'OPERATIONAL';
  if (healthScore < 50) {
    status = 'CRITICAL';
  } else if (healthScore < 80) {
    status = 'WARNING';
  }
  
  return SystemHealth(
    status: status,
    healthScore: healthScore,
    operational: operational,
    total: total,
    lastUpdate: DateTime.now().toIso8601String(),
  );
}
