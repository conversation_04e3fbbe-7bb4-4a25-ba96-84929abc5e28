{"valid_import": true, "imports": [{"uri": "package:flutter/foundation.dart", "transitive": false}, {"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:fl_chart/fl_chart.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}, {"uri": "package:frontend/presentation/routes/app_router.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/dashboard_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/dashboard_header.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/system_status_card.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/property_grid.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/alerts_feed.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/system_health_chart.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/quick_actions.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/dashboard/widgets/performance_metrics.dart", "transitive": false}], "elements": []}