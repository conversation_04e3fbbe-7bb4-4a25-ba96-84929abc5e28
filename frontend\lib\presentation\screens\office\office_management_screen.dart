import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/office.dart';
import '../../providers/office_providers.dart';
import '../main/main_navigation_screen.dart';

class OfficeManagementScreen extends ConsumerStatefulWidget {
  const OfficeManagementScreen({super.key});

  @override
  ConsumerState<OfficeManagementScreen> createState() => _OfficeManagementScreenState();
}

class _OfficeManagementScreenState extends ConsumerState<OfficeManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Office Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              // TODO: Show calendar view
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              // TODO: Download reports
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).primaryColor,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(text: 'Office Locations'),
                Tab(text: 'Construction Sites'),
              ],
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOfficeLocations(),
                _buildConstructionSites(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActions();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOfficeLocations() {
    return Consumer(
      builder: (context, ref, child) {
        final officesAsyncValue = ref.watch(officesProvider);

        return officesAsyncValue.when(
          data: (offices) => _buildDynamicOfficesList(offices),
          loading: () => _buildOfficesLoadingState(),
          error: (error, stack) => _buildOfficesErrorState(error),
        );
      },
    );
  }

  Widget _buildDynamicOfficesList(List<Office> offices) {
    if (offices.isEmpty) {
      return _buildEmptyOfficesState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(officesProvider);
        ref.invalidate(allOfficesStatisticsProvider);
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: offices.length,
        itemBuilder: (context, index) {
          final office = offices[index];
          return _buildDynamicOfficeCard(office);
        },
      ),
    );
  }

  Widget _buildOfficesLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) => _buildOfficeCardSkeleton(),
    );
  }

  Widget _buildOfficesErrorState(Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load offices',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your connection and try again',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(officesProvider);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyOfficesState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'No offices found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Offices will appear here when added',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeCard(Map<String, dynamic> office) {
    Color statusColor = office['status'] == 'operational'
        ? AppTheme.successColor
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          _showOfficeDetail(office);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Office Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          office['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          office['address'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: office['status'], showLabel: true),
                ],
              ),

              const SizedBox(height: 16),

              // Attendance Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Employees',
                      office['employees'].toString(),
                      Icons.people,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      office['presentToday'].toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Attendance Rate
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Attendance Rate',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${office['attendanceRate']}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: office['attendanceRate'] / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Quick Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickActionButton(
                    'Attendance',
                    Icons.assignment_turned_in,
                    () => _navigateToAttendance(office['id']),
                  ),
                  _buildQuickActionButton(
                    'Employees',
                    Icons.people_outline,
                    () => _navigateToEmployees(office['id']),
                  ),
                  _buildQuickActionButton(
                    'Reports',
                    Icons.assessment,
                    () => _navigateToReports(office['id']),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDynamicOfficeCard(Office office) {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsyncValue = ref.watch(officeStatisticsProvider(office.id));

        return statisticsAsyncValue.when(
          data: (statistics) => _buildOfficeCardWithStats(office, statistics),
          loading: () => _buildOfficeCardSkeleton(),
          error: (error, stack) => _buildOfficeCardWithError(office, error),
        );
      },
    );
  }

  Widget _buildOfficeCardWithStats(Office office, OfficeStatistics statistics) {
    final statusColor = _getOfficeStatusColor(statistics.attendanceRate);
    final statusText = _getOfficeStatusText(statistics.attendanceRate);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          _showDynamicOfficeDetail(office, statistics);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Office Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          office.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          office.address,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        if (office.description.isNotEmpty)
                          Text(
                            office.description,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[500],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: statusText.toLowerCase(), showLabel: true),
                ],
              ),

              const SizedBox(height: 16),

              // Attendance Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Employees',
                      statistics.totalEmployees.toString(),
                      Icons.people,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      statistics.presentToday.toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Attendance Rate
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Attendance Rate',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${statistics.attendanceRate.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: statistics.attendanceRate / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),

              if (statistics.avgCheckInTime.isNotEmpty || statistics.avgCheckOutTime.isNotEmpty) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (statistics.avgCheckInTime.isNotEmpty)
                      Expanded(
                        child: _buildTimeStatCard(
                          'Avg Check-in',
                          statistics.avgCheckInTime,
                          Icons.login,
                        ),
                      ),
                    if (statistics.avgCheckInTime.isNotEmpty && statistics.avgCheckOutTime.isNotEmpty)
                      const SizedBox(width: 12),
                    if (statistics.avgCheckOutTime.isNotEmpty)
                      Expanded(
                        child: _buildTimeStatCard(
                          'Avg Check-out',
                          statistics.avgCheckOutTime,
                          Icons.logout,
                        ),
                      ),
                  ],
                ),
              ],

              const SizedBox(height: 12),

              // Quick Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickActionButton(
                    'Attendance',
                    Icons.assignment_turned_in,
                    () => _navigateToAttendance(office.id),
                  ),
                  _buildQuickActionButton(
                    'Employees',
                    Icons.people_outline,
                    () => _navigateToEmployees(office.id),
                  ),
                  _buildQuickActionButton(
                    'Reports',
                    Icons.assessment,
                    () => _navigateToReports(office.id),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfficeStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header skeleton
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 18,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        height: 14,
                        width: 150,
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 80,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Stats skeleton
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Progress bar skeleton
            Container(
              height: 4,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeCardWithError(Office office, Object error) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Office Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        office.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        office.address,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                StatusIndicator(status: 'error', showLabel: true),
              ],
            ),

            const SizedBox(height: 16),

            // Error message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Failed to load office statistics',
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      ref.invalidate(officeStatisticsProvider(office.id));
                    },
                    child: Text('Retry', style: TextStyle(fontSize: 12)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeStatCard(String title, String time, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 16),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                time,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getOfficeStatusColor(double attendanceRate) {
    if (attendanceRate >= 90) {
      return AppTheme.successColor;
    } else if (attendanceRate >= 75) {
      return AppTheme.warningColor;
    } else {
      return AppTheme.errorColor;
    }
  }

  String _getOfficeStatusText(double attendanceRate) {
    if (attendanceRate >= 90) {
      return 'OPERATIONAL';
    } else if (attendanceRate >= 75) {
      return 'WARNING';
    } else {
      return 'CRITICAL';
    }
  }

  Widget _buildConstructionSites() {
    final sites = [
      {
        'id': 'gandipet-1',
        'name': 'Gandipet 1',
        'location': 'Gandipet, Hyderabad',
        'workers': 10,
        'presentToday': 10,
        'attendanceRate': 100,
        'status': 'operational',
        'progress': 75,
      },
      {
        'id': 'gandipet-2',
        'name': 'Gandipet 2',
        'location': 'Gandipet, Hyderabad',
        'workers': 8,
        'presentToday': 7,
        'attendanceRate': 88,
        'status': 'operational',
        'progress': 60,
      },
      {
        'id': 'gandipet-3-4',
        'name': 'Gandipet 3 & 4',
        'location': 'Gandipet, Hyderabad',
        'workers': 15,
        'presentToday': 14,
        'attendanceRate': 93,
        'status': 'operational',
        'progress': 45,
      },
      {
        'id': 'bachupally',
        'name': 'Bachupally',
        'location': 'Bachupally, Hyderabad',
        'workers': 12,
        'presentToday': 10,
        'attendanceRate': 83,
        'status': 'warning',
        'progress': 30,
      },
    ];

    return RefreshIndicator(
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sites.length,
        itemBuilder: (context, index) {
          final site = sites[index];
          return _buildConstructionSiteCard(site);
        },
      ),
    );
  }

  Widget _buildConstructionSiteCard(Map<String, dynamic> site) {
    Color statusColor = site['status'] == 'operational' 
        ? AppTheme.successColor 
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          _showSiteDetail(site);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          site['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          site['location'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: site['status'], showLabel: true),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Project Progress',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${site['progress']}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: site['progress'] / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Worker Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Workers',
                      site['workers'].toString(),
                      Icons.engineering,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      site['presentToday'].toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showOfficeDetail(Map<String, dynamic> office) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              Text(
                office['name'],
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 16),

              // Quick actions for office detail
              Text(
                'Quick Actions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 12),

              GridView.count(
                shrinkWrap: true,
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 2,
                children: [
                  _buildDetailActionCard('Submit Attendance', Icons.assignment_turned_in),
                  _buildDetailActionCard('Add/Remove Members', Icons.people),
                  _buildDetailActionCard('Download Reports', Icons.download),
                  _buildDetailActionCard('View Analytics', Icons.analytics),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDynamicOfficeDetail(Office office, OfficeStatistics statistics) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.6,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Office Header
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            office.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            office.address,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          if (office.description.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              office.description,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    StatusIndicator(
                      status: _getOfficeStatusText(statistics.attendanceRate).toLowerCase(),
                      showLabel: true,
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Statistics Overview
                Text(
                  'Today\'s Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: _buildOfficeStatCard(
                        'Total Employees',
                        statistics.totalEmployees.toString(),
                        Icons.people,
                        AppTheme.infoColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildOfficeStatCard(
                        'Present Today',
                        statistics.presentToday.toString(),
                        Icons.check_circle,
                        AppTheme.successColor,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Attendance Rate
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getOfficeStatusColor(statistics.attendanceRate).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getOfficeStatusColor(statistics.attendanceRate).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Attendance Rate',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${statistics.attendanceRate.toStringAsFixed(1)}%',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _getOfficeStatusColor(statistics.attendanceRate),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: statistics.attendanceRate / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getOfficeStatusColor(statistics.attendanceRate),
                        ),
                      ),
                    ],
                  ),
                ),

                if (statistics.avgCheckInTime.isNotEmpty || statistics.avgCheckOutTime.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Average Times',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (statistics.avgCheckInTime.isNotEmpty)
                        Expanded(
                          child: _buildTimeStatCard(
                            'Avg Check-in',
                            statistics.avgCheckInTime,
                            Icons.login,
                          ),
                        ),
                      if (statistics.avgCheckInTime.isNotEmpty && statistics.avgCheckOutTime.isNotEmpty)
                        const SizedBox(width: 12),
                      if (statistics.avgCheckOutTime.isNotEmpty)
                        Expanded(
                          child: _buildTimeStatCard(
                            'Avg Check-out',
                            statistics.avgCheckOutTime,
                            Icons.logout,
                          ),
                        ),
                    ],
                  ),
                ],

                const SizedBox(height: 24),

                // Quick Actions
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 12),

                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 2,
                  children: [
                    _buildDetailActionCard('Submit Attendance', Icons.assignment_turned_in),
                    _buildDetailActionCard('View Attendance', Icons.people),
                    _buildDetailActionCard('Download Reports', Icons.download),
                    _buildDetailActionCard('View Analytics', Icons.analytics),
                  ],
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailActionCard(String title, IconData icon) {
    return Card(
      child: InkWell(
        onTap: () {
          Navigator.pop(context);
          // TODO: Navigate to specific action
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSiteDetail(Map<String, dynamic> site) {
    // Similar to office detail but for construction sites
    _showOfficeDetail(site);
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.assignment_turned_in),
              title: const Text('Submit Attendance'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to attendance submission
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.people_alt),
              title: const Text('Add Employee'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to add employee
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Download Reports'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to reports
              },
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAttendance(String officeId) {
    context.go('/office/attendance?officeId=$officeId');
  }

  void _navigateToEmployees(String officeId) {
    context.go('/office/employees?officeId=$officeId');
  }

  void _navigateToReports(String officeId) {
    context.go('/office/reports?officeId=$officeId');
  }
}
