import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../main/main_navigation_screen.dart';

class OfficeManagementScreen extends ConsumerStatefulWidget {
  const OfficeManagementScreen({super.key});

  @override
  ConsumerState<OfficeManagementScreen> createState() => _OfficeManagementScreenState();
}

class _OfficeManagementScreenState extends ConsumerState<OfficeManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Office Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              // TODO: Show calendar view
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              // TODO: Download reports
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).primaryColor,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(text: 'Office Locations'),
                Tab(text: 'Construction Sites'),
              ],
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOfficeLocations(),
                _buildConstructionSites(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActions();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOfficeLocations() {
    final offices = [
      {
        'id': 'back-office-brane',
        'name': 'Back Office - Brane',
        'address': 'Hyderabad',
        'employees': 12,
        'presentToday': 11,
        'attendanceRate': 92,
        'status': 'operational',
      },
      {
        'id': 'strf-office-brane',
        'name': 'STRF Office - Brane',
        'address': 'Hyderabad',
        'employees': 8,
        'presentToday': 7,
        'attendanceRate': 88,
        'status': 'operational',
      },
      {
        'id': 'road-36-office',
        'name': 'Road No. 36 office - SRSR',
        'address': 'Road No. 36, Hyderabad',
        'employees': 10,
        'presentToday': 9,
        'attendanceRate': 90,
        'status': 'operational',
      },
      {
        'id': 'back-office-srsr',
        'name': 'Back Office - SRSR',
        'address': 'Hyderabad',
        'employees': 15,
        'presentToday': 13,
        'attendanceRate': 87,
        'status': 'warning',
      },
    ];

    return RefreshIndicator(
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: offices.length,
        itemBuilder: (context, index) {
          final office = offices[index];
          return _buildOfficeCard(office);
        },
      ),
    );
  }

  Widget _buildOfficeCard(Map<String, dynamic> office) {
    Color statusColor = office['status'] == 'operational' 
        ? AppTheme.successColor 
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          _showOfficeDetail(office);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Office Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          office['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          office['address'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: office['status'], showLabel: true),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Attendance Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Employees',
                      office['employees'].toString(),
                      Icons.people,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      office['presentToday'].toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Attendance Rate
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Attendance Rate',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${office['attendanceRate']}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: office['attendanceRate'] / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Quick Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickActionButton(
                    'Attendance',
                    Icons.assignment_turned_in,
                    () => _navigateToAttendance(office['id']),
                  ),
                  _buildQuickActionButton(
                    'Employees',
                    Icons.people_outline,
                    () => _navigateToEmployees(office['id']),
                  ),
                  _buildQuickActionButton(
                    'Reports',
                    Icons.assessment,
                    () => _navigateToReports(office['id']),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfficeStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConstructionSites() {
    final sites = [
      {
        'id': 'gandipet-1',
        'name': 'Gandipet 1',
        'location': 'Gandipet, Hyderabad',
        'workers': 10,
        'presentToday': 10,
        'attendanceRate': 100,
        'status': 'operational',
        'progress': 75,
      },
      {
        'id': 'gandipet-2',
        'name': 'Gandipet 2',
        'location': 'Gandipet, Hyderabad',
        'workers': 8,
        'presentToday': 7,
        'attendanceRate': 88,
        'status': 'operational',
        'progress': 60,
      },
      {
        'id': 'gandipet-3-4',
        'name': 'Gandipet 3 & 4',
        'location': 'Gandipet, Hyderabad',
        'workers': 15,
        'presentToday': 14,
        'attendanceRate': 93,
        'status': 'operational',
        'progress': 45,
      },
      {
        'id': 'bachupally',
        'name': 'Bachupally',
        'location': 'Bachupally, Hyderabad',
        'workers': 12,
        'presentToday': 10,
        'attendanceRate': 83,
        'status': 'warning',
        'progress': 30,
      },
    ];

    return RefreshIndicator(
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sites.length,
        itemBuilder: (context, index) {
          final site = sites[index];
          return _buildConstructionSiteCard(site);
        },
      ),
    );
  }

  Widget _buildConstructionSiteCard(Map<String, dynamic> site) {
    Color statusColor = site['status'] == 'operational' 
        ? AppTheme.successColor 
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          _showSiteDetail(site);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          site['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          site['location'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: site['status'], showLabel: true),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Project Progress',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${site['progress']}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: site['progress'] / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Worker Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Workers',
                      site['workers'].toString(),
                      Icons.engineering,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      site['presentToday'].toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showOfficeDetail(Map<String, dynamic> office) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              Text(
                office['name'],
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Quick actions for office detail
              Text(
                'Quick Actions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              const SizedBox(height: 12),
              
              GridView.count(
                shrinkWrap: true,
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 2,
                children: [
                  _buildDetailActionCard('Submit Attendance', Icons.assignment_turned_in),
                  _buildDetailActionCard('Add/Remove Members', Icons.people),
                  _buildDetailActionCard('Download Reports', Icons.download),
                  _buildDetailActionCard('View Analytics', Icons.analytics),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailActionCard(String title, IconData icon) {
    return Card(
      child: InkWell(
        onTap: () {
          Navigator.pop(context);
          // TODO: Navigate to specific action
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSiteDetail(Map<String, dynamic> site) {
    // Similar to office detail but for construction sites
    _showOfficeDetail(site);
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.assignment_turned_in),
              title: const Text('Submit Attendance'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to attendance submission
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.people_alt),
              title: const Text('Add Employee'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to add employee
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Download Reports'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to reports
              },
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAttendance(String officeId) {
    context.go('/office/attendance?officeId=$officeId');
  }

  void _navigateToEmployees(String officeId) {
    context.go('/office/employees?officeId=$officeId');
  }

  void _navigateToReports(String officeId) {
    context.go('/office/reports?officeId=$officeId');
  }
}
