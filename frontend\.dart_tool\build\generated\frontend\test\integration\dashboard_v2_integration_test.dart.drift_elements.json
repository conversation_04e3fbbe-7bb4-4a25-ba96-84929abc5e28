{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_test/flutter_test.dart", "transitive": false}, {"uri": "package:integration_test/integration_test.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/main.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}], "elements": []}