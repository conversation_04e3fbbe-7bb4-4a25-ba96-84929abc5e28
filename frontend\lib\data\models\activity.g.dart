// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Activity _$ActivityFromJson(Map<String, dynamic> json) => Activity(
      id: json['id'] as String,
      userId: json['userId'] as String?,
      propertyId: json['propertyId'] as String?,
      action: json['action'] as String,
      description: json['description'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      createdAt: json['createdAt'] as String,
      user: json['user'] == null
          ? null
          : UserInfo.fromJson(json['user'] as Map<String, dynamic>),
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ActivityToJson(Activity instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'propertyId': instance.propertyId,
      'action': instance.action,
      'description': instance.description,
      'metadata': instance.metadata,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'createdAt': instance.createdAt,
      'user': instance.user,
      'property': instance.property,
    };

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
    };

PropertyInfo _$PropertyInfoFromJson(Map<String, dynamic> json) => PropertyInfo(
      id: json['id'] as String,
      name: json['name'] as String,
    );

Map<String, dynamic> _$PropertyInfoToJson(PropertyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };

ActivityQueryParams _$ActivityQueryParamsFromJson(Map<String, dynamic> json) =>
    ActivityQueryParams(
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      userId: json['userId'] as String?,
      propertyId: json['propertyId'] as String?,
      action: json['action'] as String?,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
    );

Map<String, dynamic> _$ActivityQueryParamsToJson(
        ActivityQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'userId': instance.userId,
      'propertyId': instance.propertyId,
      'action': instance.action,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
    };

ActivityStatistics _$ActivityStatisticsFromJson(Map<String, dynamic> json) =>
    ActivityStatistics(
      total: (json['total'] as num).toInt(),
      today: (json['today'] as num).toInt(),
      thisWeek: (json['thisWeek'] as num).toInt(),
      thisMonth: (json['thisMonth'] as num).toInt(),
      byAction: Map<String, int>.from(json['byAction'] as Map),
      byUser: Map<String, int>.from(json['byUser'] as Map),
      byProperty: Map<String, int>.from(json['byProperty'] as Map),
    );

Map<String, dynamic> _$ActivityStatisticsToJson(ActivityStatistics instance) =>
    <String, dynamic>{
      'total': instance.total,
      'today': instance.today,
      'thisWeek': instance.thisWeek,
      'thisMonth': instance.thisMonth,
      'byAction': instance.byAction,
      'byUser': instance.byUser,
      'byProperty': instance.byProperty,
    };
