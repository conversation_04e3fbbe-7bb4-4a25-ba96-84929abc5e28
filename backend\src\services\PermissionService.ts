import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';

export interface PermissionContext {
  userId: string;
  role: UserRole;
  propertyId?: string;
  officeId?: string;
  resourceId?: string;
  timestamp?: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface PermissionResult {
  granted: boolean;
  reason?: string;
  conditions?: Record<string, any>;
}

export interface UIPermissions {
  screens: string[];
  tabs: Record<string, {
    visible: boolean;
    enabled: boolean;
    accessLevel: 'full' | 'read_only' | 'restricted' | 'none';
    restrictions?: Record<string, any>;
  }>;
  widgets: Record<string, {
    visible: boolean;
    enabled: boolean;
    conditions?: Record<string, any>;
  }>;
  actions: string[];
  dataFilters: Record<string, any>;
}

export class PermissionService {
  
  /**
   * Check if user has permission for a specific action
   */
  static async checkPermission(
    context: PermissionContext,
    resource: string,
    action: string
  ): Promise<PermissionResult> {
    try {
      // 1. Check user-specific overrides first
      const userOverride = await prisma.userPermissionOverride.findFirst({
        where: {
          userId: context.userId,
          permission: {
            resource,
            action,
          },
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        include: { permission: true }
      });

      if (userOverride) {
        await this.auditPermission(context, resource, action, userOverride.isGranted, 'User Override');
        return {
          granted: userOverride.isGranted,
          reason: userOverride.reason || 'User-specific override'
        };
      }

      // 2. Check role-based permissions
      const rolePermission = await prisma.rolePermission.findFirst({
        where: {
          role: { name: context.role },
          permission: {
            resource,
            action,
            isActive: true
          },
          isGranted: true
        },
        include: { permission: true, role: true }
      });

      if (!rolePermission) {
        await this.auditPermission(context, resource, action, false, 'No role permission');
        return { granted: false, reason: 'Permission not granted to role' };
      }

      // 3. Evaluate ABAC conditions
      const abacResult = await this.evaluateABACConditions(
        context,
        rolePermission.permission.conditions as Record<string, any>,
        rolePermission.conditions as Record<string, any>
      );

      if (!abacResult.granted) {
        await this.auditPermission(context, resource, action, false, abacResult.reason);
        return abacResult;
      }

      // 4. Check access policies
      const policyResult = await this.evaluateAccessPolicies(context, resource, action);
      
      await this.auditPermission(context, resource, action, policyResult.granted, policyResult.reason);
      return policyResult;

    } catch (error) {
      console.error('Permission check error:', error);
      await this.auditPermission(context, resource, action, false, 'System error');
      return { granted: false, reason: 'System error during permission check' };
    }
  }

  /**
   * Get comprehensive UI permissions for a user including tab-level control
   */
  static async getUserUIPermissions(context: PermissionContext): Promise<UIPermissions> {
    try {
      // Get role-based UI component permissions
      const uiPermissions = await prisma.uIComponentPermission.findMany({
        where: {
          role: { name: context.role }
        },
        include: { role: true }
      });

      // Get tab-level permissions
      const tabPermissions = await prisma.tabPermission.findMany({
        where: {
          role: { name: context.role }
        },
        include: { role: true }
      });

      // Get allowed screens
      const screens = uiPermissions
        .filter(p => p.isVisible)
        .map(p => p.screen)
        .filter((screen, index, self) => self.indexOf(screen) === index);

      // Get tab permissions
      const tabs: Record<string, any> = {};
      tabPermissions.forEach(permission => {
        const tabKey = `${permission.screen}.${permission.tabId}`;
        tabs[tabKey] = {
          visible: permission.isVisible,
          enabled: permission.isEnabled,
          accessLevel: permission.accessLevel,
          restrictions: permission.restrictions
        };
      });

      // Get widget permissions
      const widgets: Record<string, any> = {};
      uiPermissions.forEach(permission => {
        const widgetKey = permission.tab
          ? `${permission.screen}.${permission.tab}.${permission.widget}`
          : `${permission.screen}.${permission.widget}`;

        widgets[widgetKey] = {
          visible: permission.isVisible,
          enabled: permission.isEnabled,
          accessLevel: permission.accessLevel,
          conditions: permission.conditions,
          restrictions: permission.restrictions
        };
      });

      // Get allowed actions
      const rolePermissions = await prisma.rolePermission.findMany({
        where: {
          role: { name: context.role },
          isGranted: true
        },
        include: { permission: true }
      });

      const actions = rolePermissions
        .map(rp => rp.permission.action)
        .filter((action, index, self) => self.indexOf(action) === index);

      // Get data filters
      const dataFilters = await prisma.dataFilter.findMany({
        where: {
          role: { name: context.role },
          isActive: true
        }
      });

      const filters: Record<string, any> = {};
      dataFilters.forEach(filter => {
        if (!filters[filter.resource]) {
          filters[filter.resource] = {};
        }
        filters[filter.resource][filter.filterType] = filter.conditions;
      });

      return {
        screens,
        tabs,
        widgets,
        actions,
        dataFilters: filters
      };

    } catch (error) {
      console.error('Error getting UI permissions:', error);
      return {
        screens: [],
        tabs: {},
        widgets: {},
        actions: ['read'],
        dataFilters: {}
      };
    }
  }

  /**
   * Check tab-level permission for a specific screen
   */
  static async checkTabPermission(
    context: PermissionContext,
    screen: string,
    tabId: string
  ): Promise<{
    hasAccess: boolean;
    accessLevel: string;
    restrictions?: Record<string, any>;
  }> {
    try {
      const tabPermission = await prisma.tabPermission.findFirst({
        where: {
          screen,
          tabId,
          role: { name: context.role }
        }
      });

      if (!tabPermission || !tabPermission.isVisible) {
        return { hasAccess: false, accessLevel: 'none' };
      }

      return {
        hasAccess: true,
        accessLevel: tabPermission.accessLevel,
        restrictions: tabPermission.restrictions as Record<string, any>
      };

    } catch (error) {
      console.error('Error checking tab permission:', error);
      return { hasAccess: false, accessLevel: 'none' };
    }
  }

  /**
   * Apply data filtering based on user permissions
   */
  static async applyDataFilters(
    context: PermissionContext,
    resource: string,
    baseQuery: any
  ): Promise<any> {
    try {
      const dataFilters = await prisma.dataFilter.findMany({
        where: {
          role: { name: context.role },
          resource,
          isActive: true
        }
      });

      let filteredQuery = { ...baseQuery };

      dataFilters.forEach(filter => {
        const conditions = filter.conditions as Record<string, any>;
        
        switch (filter.filterType) {
          case 'WHERE':
            filteredQuery.where = {
              ...filteredQuery.where,
              ...this.processFilterConditions(conditions, context)
            };
            break;
          case 'SELECT':
            filteredQuery.select = {
              ...filteredQuery.select,
              ...conditions
            };
            break;
        }
      });

      return filteredQuery;

    } catch (error) {
      console.error('Error applying data filters:', error);
      return baseQuery;
    }
  }

  /**
   * Evaluate ABAC conditions
   */
  private static async evaluateABACConditions(
    context: PermissionContext,
    permissionConditions: Record<string, any>,
    roleConditions: Record<string, any>
  ): Promise<PermissionResult> {
    try {
      const allConditions = { ...permissionConditions, ...roleConditions };

      // Time-based conditions
      if (allConditions.timeRestriction) {
        const now = new Date();
        const currentHour = now.getHours();
        const { startHour, endHour } = allConditions.timeRestriction;
        
        if (currentHour < startHour || currentHour > endHour) {
          return { granted: false, reason: 'Access denied: Outside allowed time window' };
        }
      }

      // Property-based conditions
      if (allConditions.propertyRestriction && context.propertyId) {
        const allowedProperties = allConditions.propertyRestriction.allowedProperties || [];
        if (!allowedProperties.includes(context.propertyId)) {
          return { granted: false, reason: 'Access denied: Property not in allowed list' };
        }
      }

      // IP-based conditions
      if (allConditions.ipRestriction && context.ipAddress) {
        const allowedIPs = allConditions.ipRestriction.allowedIPs || [];
        if (!allowedIPs.includes(context.ipAddress)) {
          return { granted: false, reason: 'Access denied: IP address not allowed' };
        }
      }

      return { granted: true, conditions: allConditions };

    } catch (error) {
      console.error('ABAC evaluation error:', error);
      return { granted: false, reason: 'Error evaluating access conditions' };
    }
  }

  /**
   * Evaluate access policies
   */
  private static async evaluateAccessPolicies(
    context: PermissionContext,
    resource: string,
    action: string
  ): Promise<PermissionResult> {
    try {
      const policies = await prisma.accessPolicy.findMany({
        where: {
          resource,
          action,
          isActive: true
        },
        orderBy: { priority: 'desc' }
      });

      for (const policy of policies) {
        const conditions = policy.conditions as Record<string, any>;
        const result = await this.evaluateABACConditions(context, conditions, {});
        
        if (result.granted) {
          return {
            granted: policy.effect === 'ALLOW',
            reason: `Policy: ${policy.name}`
          };
        }
      }

      return { granted: true, reason: 'No blocking policies found' };

    } catch (error) {
      console.error('Policy evaluation error:', error);
      return { granted: false, reason: 'Error evaluating access policies' };
    }
  }

  /**
   * Process filter conditions with context substitution
   */
  private static processFilterConditions(
    conditions: Record<string, any>,
    context: PermissionContext
  ): Record<string, any> {
    const processed = { ...conditions };

    // Replace context variables
    Object.keys(processed).forEach(key => {
      if (typeof processed[key] === 'string') {
        processed[key] = processed[key]
          .replace('{{userId}}', context.userId)
          .replace('{{propertyId}}', context.propertyId || '')
          .replace('{{role}}', context.role);
      }
    });

    return processed;
  }

  /**
   * Audit permission usage
   */
  private static async auditPermission(
    context: PermissionContext,
    resource: string,
    action: string,
    granted: boolean,
    reason?: string
  ): Promise<void> {
    try {
      await prisma.permissionAudit.create({
        data: {
          userId: context.userId,
          resource,
          action,
          permission: `${resource}:${action}`,
          granted,
          reason,
          context: {
            propertyId: context.propertyId,
            officeId: context.officeId,
            resourceId: context.resourceId
          },
          ipAddress: context.ipAddress,
          userAgent: context.userAgent
        }
      });
    } catch (error) {
      console.error('Audit logging error:', error);
    }
  }
}
