import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/maintenance.dart';
import '../models/api_response.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

class MaintenanceRepository {
  final ApiService _apiService;
  final Logger _logger;

  MaintenanceRepository({
    required ApiService apiService,
    required Logger logger,
  }) : _apiService = apiService, _logger = logger;

  /// Get maintenance issues with filtering and pagination
  Future<ApiResponse<PaginatedResponse<MaintenanceIssue>>> getMaintenanceIssues({
    String? propertyId,
    String? status,
    String? priority,
    String? department,
    int page = 1,
    int limit = 20,
    String? assignedTo,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (status != null) queryParams['status'] = status;
      if (priority != null) queryParams['priority'] = priority;
      if (department != null) queryParams['department'] = department;
      if (assignedTo != null) queryParams['assignedTo'] = assignedTo;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiService.get(
        '/v1/maintenance',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final paginatedResponse = PaginatedResponse<MaintenanceIssue>.fromJson(
          jsonData,
          (json) => MaintenanceIssue.fromJson(json as Map<String, dynamic>),
        );

        _logger.info('Successfully fetched ${paginatedResponse.data.length} maintenance issues');
        
        return ApiResponse.success(paginatedResponse);
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to fetch maintenance issues: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in getMaintenanceIssues: $e', stackTrace);
      return ApiResponse.error('Failed to fetch maintenance issues: ${e.toString()}');
    }
  }

  /// Get maintenance issue details by ID
  Future<ApiResponse<MaintenanceIssueDetail>> getMaintenanceIssueDetail(String issueId) async {
    try {
      final response = await _apiService.get('/v1/maintenance/$issueId');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final issueDetail = MaintenanceIssueDetail.fromJson(jsonData['data']);

        _logger.info('Successfully fetched maintenance issue detail: $issueId');
        
        return ApiResponse.success(issueDetail);
      } else if (response.statusCode == 404) {
        return ApiResponse.error('Maintenance issue not found');
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to fetch maintenance issue detail: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in getMaintenanceIssueDetail: $e', stackTrace);
      return ApiResponse.error('Failed to fetch maintenance issue details: ${e.toString()}');
    }
  }

  /// Get maintenance statistics
  Future<ApiResponse<MaintenanceStatistics>> getMaintenanceStatistics({
    String? propertyId,
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final queryParams = <String, String>{};

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (dateFrom != null) queryParams['dateFrom'] = dateFrom;
      if (dateTo != null) queryParams['dateTo'] = dateTo;

      final response = await _apiService.get(
        '/v1/maintenance/statistics',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final statistics = MaintenanceStatistics.fromJson(jsonData['data']);

        _logger.info('Successfully fetched maintenance statistics');
        
        return ApiResponse.success(statistics);
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to fetch maintenance statistics: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in getMaintenanceStatistics: $e', stackTrace);
      return ApiResponse.error('Failed to fetch maintenance statistics: ${e.toString()}');
    }
  }

  /// Create a new maintenance issue
  Future<ApiResponse<MaintenanceIssue>> createMaintenanceIssue({
    required String propertyId,
    required String title,
    required String description,
    required String priority,
    required String department,
    String? assignedTo,
    List<String>? attachments,
    String? recurrence,
  }) async {
    try {
      final requestBody = {
        'propertyId': propertyId,
        'title': title,
        'description': description,
        'priority': priority,
        'department': department,
        if (assignedTo != null) 'assignedTo': assignedTo,
        if (attachments != null) 'attachments': attachments,
        if (recurrence != null) 'recurrence': recurrence,
      };

      final response = await _apiService.post(
        '/v1/maintenance',
        body: requestBody,
      );

      if (response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        final issue = MaintenanceIssue.fromJson(jsonData['data']);

        _logger.info('Successfully created maintenance issue: ${issue.id}');
        
        return ApiResponse.success(issue);
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to create maintenance issue: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in createMaintenanceIssue: $e', stackTrace);
      return ApiResponse.error('Failed to create maintenance issue: ${e.toString()}');
    }
  }

  /// Update maintenance issue
  Future<ApiResponse<MaintenanceIssue>> updateMaintenanceIssue({
    required String issueId,
    String? title,
    String? description,
    String? status,
    String? priority,
    String? assignedTo,
    String? resolvedDate,
    String? actualCost,
  }) async {
    try {
      final requestBody = <String, dynamic>{};

      if (title != null) requestBody['title'] = title;
      if (description != null) requestBody['description'] = description;
      if (status != null) requestBody['status'] = status;
      if (priority != null) requestBody['priority'] = priority;
      if (assignedTo != null) requestBody['assignedTo'] = assignedTo;
      if (resolvedDate != null) requestBody['resolvedDate'] = resolvedDate;
      if (actualCost != null) requestBody['actualCost'] = actualCost;

      final response = await _apiService.put(
        '/v1/maintenance/$issueId',
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final issue = MaintenanceIssue.fromJson(jsonData['data']);

        _logger.info('Successfully updated maintenance issue: $issueId');
        
        return ApiResponse.success(issue);
      } else if (response.statusCode == 404) {
        return ApiResponse.error('Maintenance issue not found');
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to update maintenance issue: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in updateMaintenanceIssue: $e', stackTrace);
      return ApiResponse.error('Failed to update maintenance issue: ${e.toString()}');
    }
  }

  /// Delete maintenance issue
  Future<ApiResponse<bool>> deleteMaintenanceIssue(String issueId) async {
    try {
      final response = await _apiService.delete('/v1/maintenance/$issueId');

      if (response.statusCode == 200 || response.statusCode == 204) {
        _logger.info('Successfully deleted maintenance issue: $issueId');
        return ApiResponse.success(true);
      } else if (response.statusCode == 404) {
        return ApiResponse.error('Maintenance issue not found');
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to delete maintenance issue: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in deleteMaintenanceIssue: $e', stackTrace);
      return ApiResponse.error('Failed to delete maintenance issue: ${e.toString()}');
    }
  }

  /// Add comment to maintenance issue
  Future<ApiResponse<MaintenanceComment>> addComment({
    required String issueId,
    required String comment,
  }) async {
    try {
      final requestBody = {
        'comment': comment,
      };

      final response = await _apiService.post(
        '/v1/maintenance/$issueId/comments',
        body: requestBody,
      );

      if (response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        final commentObj = MaintenanceComment.fromJson(jsonData['data']);

        _logger.info('Successfully added comment to maintenance issue: $issueId');
        
        return ApiResponse.success(commentObj);
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to add comment: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in addComment: $e', stackTrace);
      return ApiResponse.error('Failed to add comment: ${e.toString()}');
    }
  }

  /// Upload attachment to maintenance issue
  Future<ApiResponse<MaintenanceAttachment>> uploadAttachment({
    required String issueId,
    required String filePath,
    required String fileName,
  }) async {
    try {
      final response = await _apiService.uploadFile(
        '/v1/maintenance/$issueId/attachments',
        filePath: filePath,
        fileName: fileName,
      );

      if (response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        final attachment = MaintenanceAttachment.fromJson(jsonData['data']);

        _logger.info('Successfully uploaded attachment to maintenance issue: $issueId');
        
        return ApiResponse.success(attachment);
      } else {
        final errorMessage = _extractErrorMessage(response);
        _logger.error('Failed to upload attachment: $errorMessage');
        return ApiResponse.error(errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.error('Exception in uploadAttachment: $e', stackTrace);
      return ApiResponse.error('Failed to upload attachment: ${e.toString()}');
    }
  }

  String _extractErrorMessage(http.Response response) {
    try {
      final jsonData = json.decode(response.body);
      return jsonData['message'] ?? jsonData['error'] ?? 'Unknown error occurred';
    } catch (e) {
      return 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
    }
  }
}

/// Paginated response wrapper
class PaginatedResponse<T> {
  final List<T> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;

  const PaginatedResponse({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    final dataList = (json['data'] as List)
        .map((item) => fromJsonT(item))
        .toList();

    return PaginatedResponse<T>(
      data: dataList,
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      totalPages: json['totalPages'] ?? 1,
      hasNext: json['hasNext'] ?? false,
      hasPrevious: json['hasPrevious'] ?? false,
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'data': data.map((item) => toJsonT(item)).toList(),
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrevious': hasPrevious,
    };
  }
}
