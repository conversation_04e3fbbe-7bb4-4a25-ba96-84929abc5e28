{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/core/constants/app_constants.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}, {"uri": "package:frontend/presentation/routes/app_router.dart", "transitive": false}], "elements": []}