import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';

import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import middleware
import { errorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { endpointSpecificRateLimit } from '@/middleware/rateLimiter';

// Import routes
import authRoutes from '@/routes/auth';
import propertiesRoutes from '@/routes/properties';
import dashboardRoutes from '@/routes/dashboard';
import officesRoutes from '@/routes/offices';
import usersRoutes from '@/routes/users';
import reportsRoutes from '@/routes/reports';
import notificationsRoutes from '@/routes/notifications';
import filesRoutes from '@/routes/files';
import maintenanceRoutes from '@/routes/maintenance';
import ottRoutes from '@/routes/ott';

// Import services
import { prisma } from '@/lib/prisma';

const app = express();
const PORT = process.env.PORT || 3000;
const API_VERSION = process.env.API_VERSION || 'v1';

// Create HTTP server
const server = createServer(app);

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.3',
    info: {
      title: 'SRSR Property Management API',
      version: '1.0.0',
      description: 'Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities.',
      contact: {
        name: 'SRSR Property Management',
        email: '<EMAIL>',
        url: 'https://srsrproperty.com',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `http://localhost:${PORT}/${API_VERSION}`,
        description: 'Development server',
      },
      {
        url: `https://api.srsrproperty.com/${API_VERSION}`,
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication. Include in Authorization header as \'Bearer {token}\'',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.ts'], // Path to the API files
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
const corsOptions = {
  origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // In development, allow all localhost and 127.0.0.1 origins
    if (process.env.NODE_ENV !== 'production') {
      if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
        return callback(null, true);
      }
    }

    // Check against allowed origins
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8080',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
      'http://127.0.0.1:8080'
    ];

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.log(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));
}

// Rate limiting
app.use(endpointSpecificRateLimit);

// Health check endpoint (no auth required)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// Detailed health check endpoint
app.get('/health/detailed', async (req, res) => {
  try {
    // Test database connection
    const dbStart = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const dbResponseTime = Date.now() - dbStart;

    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: {
          status: 'healthy',
          responseTime: dbResponseTime,
          lastCheck: new Date().toISOString(),
        },
        storage: {
          status: 'healthy',
          responseTime: 0,
          lastCheck: new Date().toISOString(),
        },
      },
      metrics: {
        memoryUsage: Math.round(memoryUsagePercent),
        cpuUsage: 0, // Would need additional monitoring for this
        activeConnections: 0, // Would need connection tracking
      },
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// API Documentation
if (process.env.SWAGGER_ENABLED === 'true') {
  app.use(
    process.env.SWAGGER_PATH || '/api-docs',
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'SRSR Property Management API',
    })
  );
}

// API routes
app.use(`/${API_VERSION}/auth`, authRoutes);
app.use(`/${API_VERSION}/properties`, propertiesRoutes);
app.use(`/${API_VERSION}/dashboard`, dashboardRoutes);
app.use(`/${API_VERSION}/offices`, officesRoutes);
app.use(`/${API_VERSION}/users`, usersRoutes);
app.use(`/${API_VERSION}/reports`, reportsRoutes);
app.use(`/${API_VERSION}/notifications`, notificationsRoutes);
app.use(`/${API_VERSION}/files`, filesRoutes);
app.use(`/${API_VERSION}/maintenance`, maintenanceRoutes);
app.use(`/${API_VERSION}/ott`, ottRoutes);



// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  
  server.close(() => {
    console.log('HTTP server closed');
  });
  
  await prisma.$disconnect();
  console.log('Database connection closed');
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  
  server.close(() => {
    console.log('HTTP server closed');
  });
  
  await prisma.$disconnect();
  console.log('Database connection closed');
  
  process.exit(0);
});

// Start server
if (process.env.NODE_ENV !== 'test') {
  server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📚 API Documentation: http://localhost:${PORT}${process.env.SWAGGER_PATH || '/api-docs'}`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);

  });
}

export default app;
