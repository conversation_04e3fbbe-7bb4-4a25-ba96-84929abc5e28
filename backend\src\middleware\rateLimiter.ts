import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRes } from 'rate-limiter-flexible';

// Rate limiter configurations
const rateLimiters = {
  // General API rate limiter
  general: new RateLimiterMemory({
    points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000, // 15 minutes
    blockDuration: 60, // Block for 1 minute
  }),

  // Authentication rate limiter (stricter)
  auth: new RateLimiterMemory({
    points: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5'),
    duration: 900, // 15 minutes
    blockDuration: 900, // Block for 15 minutes
  }),

  // Write operations rate limiter
  write: new RateLimiterMemory({
    points: 30,
    duration: 900, // 15 minutes
    blockDuration: 60, // Block for 1 minute
  }),

  // File upload rate limiter
  upload: new RateLimiterMemory({
    points: 10,
    duration: 3600, // 1 hour
    blockDuration: 300, // Block for 5 minutes
  }),

  // Per-user rate limiter for authenticated requests
  user: new RateLimiterMemory({
    points: 200,
    duration: 900, // 15 minutes
    blockDuration: 60, // Block for 1 minute
  }),
};

// Key generators for different rate limiters
const keyGenerators = {
  general: (req: Request) => req.ip,
  auth: (req: Request) => req.ip,
  write: (req: Request) => req.ip,
  upload: (req: Request) => req.ip,
  user: (req: Request) => req.user?.id || req.ip,
};

// Generic rate limiter middleware factory
export const createRateLimiter = (
  limiterName: keyof typeof rateLimiters,
  customMessage?: string
) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const limiter = rateLimiters[limiterName];
    const keyGenerator = keyGenerators[limiterName];
    const key = keyGenerator(req);

    try {
      const resRateLimiter = await limiter.consume(key);

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': limiter.points.toString(),
        'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
        'X-RateLimit-Reset': new Date(Date.now() + (resRateLimiter.msBeforeNext || 60000)).toISOString(),
      });

      next();
    } catch (rateLimiterRes) {
      const result = rateLimiterRes as RateLimiterRes;
      const msBeforeNext = result.msBeforeNext || 60000; // Default to 1 minute
      const retryAfter = Math.round(msBeforeNext / 1000);

      res.set({
        'X-RateLimit-Limit': limiter.points.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
        'Retry-After': retryAfter.toString(),
      });

      res.status(429).json({
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: customMessage || 'Too many requests. Please try again later.',
        timestamp: new Date().toISOString(),
        path: req.path,
        retryAfter,
      });
    }
  };
};

// Specific rate limiter middlewares
export const generalRateLimit = createRateLimiter('general');

export const authRateLimit = createRateLimiter(
  'auth',
  'Too many authentication attempts. Please try again in 15 minutes.'
);

export const writeRateLimit = createRateLimiter(
  'write',
  'Too many write operations. Please try again later.'
);

export const uploadRateLimit = createRateLimiter(
  'upload',
  'Too many file uploads. Please try again later.'
);

export const userRateLimit = createRateLimiter('user');

// Conditional rate limiter based on request method
export const conditionalRateLimit = (req: Request, res: Response, next: NextFunction): void => {
  const method = req.method.toUpperCase();
  
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    writeRateLimit(req, res, next);
  } else {
    generalRateLimit(req, res, next);
  }
};

// IP-based rate limiter with different limits for different endpoints
export const endpointSpecificRateLimit = (req: Request, res: Response, next: NextFunction): void => {
  const path = req.path.toLowerCase();
  
  if (path.includes('/auth/')) {
    authRateLimit(req, res, next);
  } else if (path.includes('/upload') || path.includes('/files')) {
    uploadRateLimit(req, res, next);
  } else if (req.user) {
    userRateLimit(req, res, next);
  } else {
    generalRateLimit(req, res, next);
  }
};

// Reset rate limiter for a specific key (useful for testing or admin actions)
export const resetRateLimit = async (key: string, limiterName: keyof typeof rateLimiters): Promise<void> => {
  const limiter = rateLimiters[limiterName];
  await limiter.delete(key);
};

// Get rate limit status for a key
export const getRateLimitStatus = async (
  key: string,
  limiterName: keyof typeof rateLimiters
): Promise<RateLimiterRes | null> => {
  const limiter = rateLimiters[limiterName];
  return limiter.get(key);
};

// Middleware to add rate limit info to response headers (for monitoring)
export const addRateLimitHeaders = (limiterName: keyof typeof rateLimiters) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const limiter = rateLimiters[limiterName];
    const keyGenerator = keyGenerators[limiterName];
    const key = keyGenerator(req);

    try {
      const status = await limiter.get(key);

      if (status) {
        res.set({
          'X-RateLimit-Limit': limiter.points.toString(),
          'X-RateLimit-Remaining': status.remainingPoints?.toString() || '0',
          'X-RateLimit-Reset': new Date(Date.now() + (status.msBeforeNext || 60000)).toISOString(),
        });
      } else {
        res.set({
          'X-RateLimit-Limit': limiter.points.toString(),
          'X-RateLimit-Remaining': limiter.points.toString(),
        });
      }
    } catch (error) {
      console.warn('Failed to get rate limit status:', error);
    }

    next();
  };
};
