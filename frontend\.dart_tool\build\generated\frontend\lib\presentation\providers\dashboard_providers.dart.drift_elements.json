{"valid_import": true, "imports": [{"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/data/models/dashboard.dart", "transitive": false}, {"uri": "package:frontend/data/models/alert.dart", "transitive": false}, {"uri": "package:frontend/data/models/activity.dart", "transitive": false}, {"uri": "package:frontend/data/models/api_response.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/dashboard_repository.dart", "transitive": false}], "elements": []}