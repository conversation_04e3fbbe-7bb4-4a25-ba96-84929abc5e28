{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": false, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/middleware/*": ["./src/middleware/*"], "@/routes/*": ["./src/routes/*"], "@/services/*": ["./src/services/*"], "@/models/*": ["./src/models/*"]}, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist"}, "include": ["src/**/*.ts", "prisma/seed.ts"], "exclude": ["node_modules", "dist"]}