// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String    @id @default(uuid())
  name      String
  email     String    @unique
  phone     String
  password  String
  role      UserRole
  isActive  Boolean   @default(true)
  avatar    String?
  timezone  String    @default("Asia/Kolkata")
  language  String    @default("en")
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  lastLogin DateTime?

  // Relationships
  assignedProperties UserProperty[]
  attendanceRecords  AttendanceRecord[]
  activities         Activity[]
  refreshTokens      RefreshToken[]
  notifications      Notification[]
  reportedIssues     MaintenanceIssue[] @relation("ReportedIssues")
  assignedIssues     MaintenanceIssue[] @relation("AssignedIssues")

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

// Property Management
model Property {
  id          String       @id @default(uuid())
  name        String
  type        PropertyType
  address     String
  description String
  isActive    Boolean      @default(true)
  latitude    Float?
  longitude   Float?
  images      String[]     @default([])
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relationships
  assignedUsers        UserProperty[]
  systemStatuses       SystemStatus[]
  activities           Activity[]
  alerts               Alert[]
  Notification         Notification[]
  maintenanceIssues    MaintenanceIssue[]
  maintenanceFunctions MaintenanceFunction[]
  ottServices          OTTService[]
  waterSystems         WaterSystem[]
  electricitySystems   ElectricitySystem[]
  securitySystems      SecuritySystem[]
  dashboardMetrics     DashboardMetrics[]

  @@map("properties")
}

model UserProperty {
  id         String @id @default(uuid())
  userId     String
  propertyId String

  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@unique([userId, propertyId])
  @@map("user_properties")
}

// System Monitoring
model SystemStatus {
  id          String           @id @default(uuid())
  propertyId  String
  systemType  SystemType
  status      SystemStatusEnum
  description String?
  metadata    Json?            @default("{}")
  healthScore Float?           @default(100)
  lastChecked DateTime         @default(now())
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@unique([propertyId, systemType])
  @@map("system_statuses")
}

// Office Management
model Office {
  id           String     @id @default(uuid())
  name         String
  type         OfficeType
  address      String
  latitude     Float?
  longitude    Float?
  isActive     Boolean    @default(true)
  workingHours Json?      @default("{}")
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // Relationships
  attendanceRecords AttendanceRecord[]
  employees         Employee[]

  @@map("offices")
}

model Employee {
  id          String   @id @default(uuid())
  officeId    String
  name        String
  email       String?
  phone       String?
  employeeId  String   @unique
  designation String
  department  String?
  isActive    Boolean  @default(true)
  joinDate    DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  office            Office             @relation(fields: [officeId], references: [id], onDelete: Cascade)
  attendanceRecords AttendanceRecord[]

  @@map("employees")
}

model AttendanceRecord {
  id           String           @id @default(uuid())
  officeId     String
  employeeId   String?
  userId       String?
  date         DateTime
  status       AttendanceStatus
  checkInTime  DateTime?
  checkOutTime DateTime?
  hoursWorked  Float?           @default(0)
  overtime     Float?           @default(0)
  notes        String?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  office   Office    @relation(fields: [officeId], references: [id], onDelete: Cascade)
  employee Employee? @relation(fields: [employeeId], references: [id], onDelete: SetNull)
  user     User?     @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@unique([officeId, employeeId, date])
  @@unique([officeId, userId, date])
  @@map("attendance_records")
}

// Alerts and Notifications
model Alert {
  id         String        @id @default(uuid())
  propertyId String?
  title      String
  message    String
  severity   AlertSeverity
  status     AlertStatus   @default(OPEN)
  category   String?
  metadata   Json?         @default("{}")
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  resolvedAt DateTime?

  property Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)

  @@map("alerts")
}

// Activity Logging
model Activity {
  id          String   @id @default(uuid())
  userId      String?
  propertyId  String?
  action      String
  description String
  metadata    Json?    @default("{}")
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  user     User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  property Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)

  @@map("activities")
}

// Notifications
model Notification {
  id           String               @id @default(uuid())
  title        String
  message      String
  type         NotificationType
  priority     NotificationPriority @default(MEDIUM)
  targetUsers  String[]             @default([])
  targetRoles  UserRole[]           @default([])
  propertyId   String?
  isGlobal     Boolean              @default(false)
  readBy       String[]             @default([])
  scheduledFor DateTime?
  expiresAt    DateTime?
  createdBy    String
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt

  property Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)
  creator  User      @relation(fields: [createdBy], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// OTT Services Management
model OTTService {
  id            String    @id @default(uuid())
  propertyId    String
  platform      String
  plan          String
  loginId       String?
  password      String?
  nextPayment   DateTime?
  status        OTTStatus @default(PENDING)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relationships
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("ott_services")
}

// Maintenance Management
model Department {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  maintenanceIssues    MaintenanceIssue[]
  maintenanceFunctions MaintenanceFunction[]

  @@map("departments")
}

model MaintenanceIssue {
  id              String                @id @default(uuid())
  title           String
  description     String?
  departmentId    String
  propertyId      String?
  priority        MaintenancePriority
  status          MaintenanceStatus     @default(OPEN)
  startDate       DateTime
  expectedEndDate DateTime?
  actualEndDate   DateTime?
  reportedBy      String
  assignedTo      String?
  isRecurring     Boolean               @default(false)
  recurrenceType  RecurrenceType?
  nextDueDate     DateTime?
  remarks         String?
  attachments     String[]              @default([])
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt

  // Relationships
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  property   Property?  @relation(fields: [propertyId], references: [id], onDelete: SetNull)
  reporter   User       @relation("ReportedIssues", fields: [reportedBy], references: [id], onDelete: Cascade)
  assignee   User?      @relation("AssignedIssues", fields: [assignedTo], references: [id], onDelete: SetNull)

  @@map("maintenance_issues")
}

model MaintenanceFunction {
  id                String   @id @default(uuid())
  name              String
  subFunction       String?
  departmentId      String
  propertyId        String?
  input             String?
  process           String?
  output            String?
  thresholdLimits   String?
  responsibleAgent  String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  property   Property?  @relation(fields: [propertyId], references: [id], onDelete: SetNull)

  @@map("maintenance_functions")
}

// Detailed System Monitoring Tables
model WaterSystem {
  id              String   @id @default(uuid())
  propertyId      String
  tankName        String
  capacity        Float    // in liters
  currentLevel    Float    // in liters
  levelPercentage Float    // calculated percentage
  pumpStatus      String   // ON, OFF, MAINTENANCE
  flowRate        Float?   // liters per minute
  pressure        Float?   // in PSI
  quality         String?  // GOOD, FAIR, POOR
  lastMaintenance DateTime?
  nextMaintenance DateTime?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("water_systems")
}

model ElectricitySystem {
  id                String   @id @default(uuid())
  propertyId        String
  systemName        String
  generatorStatus   String   // ON, OFF, MAINTENANCE, STANDBY
  fuelLevel         Float?   // percentage
  powerConsumption  Float?   // in kWh
  voltage           Float?   // in volts
  frequency         Float?   // in Hz
  loadPercentage    Float?   // percentage of total capacity
  mainsPowerStatus  String   // AVAILABLE, UNAVAILABLE
  batteryBackup     Float?   // percentage
  lastMaintenance   DateTime?
  nextMaintenance   DateTime?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("electricity_systems")
}

model SecuritySystem {
  id              String   @id @default(uuid())
  propertyId      String
  systemName      String
  cameraCount     Int      @default(0)
  activeCameras   Int      @default(0)
  accessPoints    Int      @default(0)
  activeAccess    Int      @default(0)
  alarmStatus     String   // ARMED, DISARMED, TRIGGERED, MAINTENANCE
  motionDetected  Boolean  @default(false)
  lastIncident    DateTime?
  lastMaintenance DateTime?
  nextMaintenance DateTime?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  cameras  SecurityCamera[]

  @@map("security_systems")
}

model SecurityCamera {
  id               String   @id @default(uuid())
  securitySystemId String
  cameraName       String
  location         String
  status           String   // ONLINE, OFFLINE, MAINTENANCE
  ipAddress        String?
  recordingStatus  String   // RECORDING, STOPPED, ERROR
  lastPing         DateTime?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  securitySystem SecuritySystem @relation(fields: [securitySystemId], references: [id], onDelete: Cascade)

  @@map("security_cameras")
}

model DashboardMetrics {
  id                    String   @id @default(uuid())
  propertyId            String?
  totalProperties       Int      @default(0)
  activeProperties      Int      @default(0)
  totalAlerts           Int      @default(0)
  criticalAlerts        Int      @default(0)
  systemsOperational    Int      @default(0)
  systemsTotal          Int      @default(0)
  maintenanceOpen       Int      @default(0)
  maintenanceOverdue    Int      @default(0)
  occupancyRate         Float?   // percentage
  energyConsumption     Float?   // kWh
  waterConsumption      Float?   // liters
  date                  DateTime @default(now())
  createdAt             DateTime @default(now())

  property Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)

  @@unique([propertyId, date])
  @@map("dashboard_metrics")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  PROPERTY_MANAGER
  OFFICE_MANAGER
  SECURITY_PERSONNEL
  MAINTENANCE_STAFF
  CONSTRUCTION_SUPERVISOR
}

enum PropertyType {
  RESIDENTIAL
  OFFICE
  CONSTRUCTION
}

enum SystemType {
  WATER
  ELECTRICITY
  SECURITY
  INTERNET
  OTT
  MAINTENANCE
}

enum SystemStatusEnum {
  OPERATIONAL
  WARNING
  CRITICAL
  OFFLINE
}

enum OfficeType {
  OFFICE
  CONSTRUCTION_SITE
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  LEAVE
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  OPEN
  ACKNOWLEDGED
  RESOLVED
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum MaintenancePriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum MaintenanceStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
  RESOLVED
  CLOSED
  ON_HOLD
}

enum RecurrenceType {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum OTTStatus {
  PENDING
  ACTIVE
  EXPIRED
  CANCELLED
}
