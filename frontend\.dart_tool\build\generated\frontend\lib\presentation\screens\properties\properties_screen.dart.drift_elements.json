{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/data/models/property.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/properties_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}], "elements": []}